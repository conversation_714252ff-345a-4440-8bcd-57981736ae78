#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查训练进度修复效果
"""

import sqlite3
import json
import time
from datetime import datetime, timedelta

def monitor_current_training():
    """监控当前训练任务"""
    print("📊 监控当前训练任务进度")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 获取运行中的任务
        cursor.execute('''
            SELECT id, status, progress, current_epoch, total_epochs, train_loss, val_loss, logs, updated_at
            FROM training_tasks
            WHERE status = 'running'
            ORDER BY updated_at DESC
        ''')
        
        tasks = cursor.fetchall()
        conn.close()
        
        if not tasks:
            print("ℹ️ 没有运行中的训练任务")
            return
        
        print(f"📋 找到 {len(tasks)} 个运行中的任务")
        
        for task in tasks:
            (task_id, status, progress, current_epoch, total_epochs, 
             train_loss, val_loss, logs, updated_at) = task
            
            print(f"\n📋 任务: {task_id[:8]}...")
            print(f"   状态: {status}")
            print(f"   进度: {progress:.2f}%")
            print(f"   轮次: {current_epoch}/{total_epochs}")
            print(f"   训练损失: {train_loss}")
            print(f"   验证损失: {val_loss}")
            print(f"   更新时间: {updated_at}")
            
            # 分析日志
            if logs:
                try:
                    log_data = json.loads(logs)
                    stage = log_data.get('stage', 'unknown')
                    message = log_data.get('message', '')
                    print(f"   阶段: {stage}")
                    print(f"   消息: {message}")
                except:
                    print(f"   日志: {logs[:100]}...")
            
            # 检查是否卡住
            if updated_at:
                try:
                    updated_time = datetime.fromisoformat(updated_at.replace('T', ' '))
                    now = datetime.now()
                    time_diff = now - updated_time
                    
                    print(f"   距离上次更新: {time_diff}")
                    
                    if time_diff > timedelta(minutes=5):
                        print(f"   ⚠️ 可能卡住：超过5分钟无更新")
                        
                        # 分析卡住原因
                        if current_epoch == 0 and progress > 25:
                            print(f"   🔍 问题：进度{progress:.1f}%但轮次为0，训练循环可能未启动")
                        elif train_loss == 0 and val_loss == 0:
                            print(f"   🔍 问题：损失为0，训练可能未真正开始")
                    else:
                        print(f"   ✅ 最近有更新，训练正常")
                        
                        # 检查训练是否有进展
                        if current_epoch > 0:
                            print(f"   ✅ 轮次正常递增")
                        if train_loss > 0:
                            print(f"   ✅ 训练损失正常")
                            
                except Exception as e:
                    print(f"   ❌ 时间解析失败: {e}")
        
        # 实时监控最活跃的任务
        if tasks:
            most_recent_task = tasks[0]
            task_id = most_recent_task[0]
            print(f"\n🔄 开始实时监控最新任务: {task_id[:8]}...")
            monitor_task_realtime(task_id)
            
    except Exception as e:
        print(f"❌ 监控失败: {e}")

def monitor_task_realtime(task_id, duration=60):
    """实时监控单个任务"""
    print(f"监控时长: {duration}秒")
    
    last_progress = None
    last_epoch = None
    last_loss = None
    update_count = 0
    
    for i in range(duration // 5):  # 每5秒检查一次
        try:
            conn = sqlite3.connect('trading_system.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT progress, current_epoch, train_loss, val_loss, updated_at
                FROM training_tasks
                WHERE id = ?
            ''', (task_id,))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                progress, epoch, train_loss, val_loss, updated_at = result
                
                elapsed = i * 5
                print(f"[{elapsed:3d}s] 进度: {progress:6.2f}%, 轮次: {epoch:2d}, 训练损失: {train_loss:.4f}, 更新: {updated_at}")
                
                # 检查是否有更新
                if (progress != last_progress or epoch != last_epoch or train_loss != last_loss):
                    update_count += 1
                    print(f"       ✅ 检测到更新 #{update_count}")
                    
                    if epoch != last_epoch and epoch > 0:
                        print(f"       📈 轮次增加: {last_epoch} -> {epoch}")
                    
                    if train_loss != last_loss and train_loss > 0:
                        print(f"       📉 损失变化: {last_loss} -> {train_loss}")
                
                last_progress = progress
                last_epoch = epoch
                last_loss = train_loss
                
            else:
                print(f"[{i*5:3d}s] ❌ 任务不存在")
                break
                
        except Exception as e:
            print(f"[{i*5:3d}s] ❌ 监控异常: {e}")
        
        time.sleep(5)
    
    print(f"\n📊 监控结果:")
    print(f"   总更新次数: {update_count}")
    
    if update_count >= 3:
        print("   ✅ 训练进度正常更新，修复成功！")
    elif update_count > 0:
        print("   ⚠️ 有少量更新，可能部分修复")
    else:
        print("   ❌ 无更新，训练仍然卡住")

def analyze_training_issues():
    """分析训练问题"""
    print(f"\n🔍 分析训练问题")
    print("-" * 30)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 统计各种状态的任务
        cursor.execute('''
            SELECT status, COUNT(*) as count
            FROM training_tasks
            GROUP BY status
        ''')
        
        status_stats = cursor.fetchall()
        
        print("📊 任务状态统计:")
        for status, count in status_stats:
            print(f"   {status}: {count} 个")
        
        # 查找可能有问题的任务
        cursor.execute('''
            SELECT id, progress, current_epoch, train_loss, updated_at
            FROM training_tasks
            WHERE status = 'running'
              AND (
                (progress > 25 AND current_epoch = 0) OR
                (train_loss = 0 AND val_loss = 0 AND progress > 0)
              )
            ORDER BY updated_at DESC
        ''')
        
        problematic_tasks = cursor.fetchall()
        
        if problematic_tasks:
            print(f"\n⚠️ 发现 {len(problematic_tasks)} 个可能有问题的任务:")
            for task in problematic_tasks:
                task_id, progress, epoch, loss, updated_at = task
                print(f"   {task_id[:8]}... | 进度: {progress:.1f}% | 轮次: {epoch} | 损失: {loss}")
        else:
            print("\n✅ 没有发现明显有问题的任务")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")

def main():
    """主函数"""
    print("🔍 训练进度修复效果检查")
    print("=" * 60)
    
    # 1. 监控当前训练
    monitor_current_training()
    
    # 2. 分析训练问题
    analyze_training_issues()
    
    print(f"\n🎉 检查完成！")
    print("=" * 60)
    print("📋 修复效果总结:")
    print("✅ 如果看到训练进度正常更新，说明修复成功")
    print("✅ 如果轮次正常递增，说明训练循环正常工作")
    print("✅ 如果损失值正常变化，说明训练真正在进行")

if __name__ == "__main__":
    main()
