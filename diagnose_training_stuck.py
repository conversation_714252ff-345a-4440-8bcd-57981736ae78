#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度诊断训练进度卡住问题
"""

import sqlite3
import json
import os
import time
import threading
from datetime import datetime, timed<PERSON><PERSON>

def get_stuck_training_details():
    """获取卡住训练任务的详细信息"""
    print("🔍 深度诊断训练进度卡住问题")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 获取所有running状态的任务
        cursor.execute('''
            SELECT id, model_id, status, progress, current_epoch, total_epochs,
                   train_loss, val_loss, logs, created_at, started_at, updated_at
            FROM training_tasks
            WHERE status = 'running'
            ORDER BY updated_at DESC
        ''')
        
        tasks = cursor.fetchall()
        conn.close()
        
        if not tasks:
            print("ℹ️ 没有找到运行中的训练任务")
            return None
        
        print(f"📊 找到 {len(tasks)} 个运行中的训练任务")
        
        for task in tasks:
            (task_id, model_id, status, progress, current_epoch, total_epochs,
             train_loss, val_loss, logs, created_at, started_at, updated_at) = task
            
            print(f"\n📋 任务详细分析: {task_id[:8]}...")
            print("-" * 50)
            
            print(f"📊 基本信息:")
            print(f"   任务ID: {task_id}")
            print(f"   模型ID: {model_id}")
            print(f"   状态: {status}")
            print(f"   进度: {progress}%")
            print(f"   当前轮次: {current_epoch}/{total_epochs}")
            print(f"   训练损失: {train_loss}")
            print(f"   验证损失: {val_loss}")
            
            print(f"\n⏰ 时间分析:")
            print(f"   创建时间: {created_at}")
            print(f"   开始时间: {started_at}")
            print(f"   更新时间: {updated_at}")
            
            # 计算卡住时长
            if updated_at:
                try:
                    updated_time = datetime.fromisoformat(updated_at.replace('T', ' '))
                    now = datetime.now()
                    stuck_duration = now - updated_time
                    print(f"   卡住时长: {stuck_duration}")
                    
                    if stuck_duration > timedelta(minutes=5):
                        print(f"   ⚠️ 警告: 超过5分钟没有更新，可能卡住")
                except Exception as e:
                    print(f"   ❌ 时间解析失败: {e}")
            
            # 分析日志
            print(f"\n📝 日志分析:")
            if logs:
                try:
                    log_data = json.loads(logs)
                    stage = log_data.get('stage', 'unknown')
                    message = log_data.get('message', '')
                    
                    print(f"   阶段: {stage}")
                    print(f"   消息: {message}")
                    
                    # 检查是否有错误信息
                    if 'error' in log_data:
                        print(f"   ❌ 错误: {log_data['error']}")
                    
                    # 检查训练相关信息
                    if 'epoch' in log_data:
                        print(f"   日志中的轮次: {log_data['epoch']}")
                    if 'batch' in log_data:
                        print(f"   日志中的批次: {log_data['batch']}")
                        
                except json.JSONDecodeError:
                    print(f"   ❌ 日志解析失败: {logs[:200]}...")
            else:
                print("   ⚠️ 无日志信息")
            
            # 问题诊断
            print(f"\n🔍 问题诊断:")
            issues = []
            
            # 检查进度与轮次不匹配
            if progress > 0 and current_epoch == 0:
                issues.append("进度显示有值但轮次为0，训练循环可能未启动")
            
            # 检查损失为0
            if train_loss == 0.0 and val_loss == 0.0:
                issues.append("训练损失和验证损失都为0，训练可能未真正开始")
            
            # 检查长时间无更新
            if updated_at:
                try:
                    updated_time = datetime.fromisoformat(updated_at.replace('T', ' '))
                    if datetime.now() - updated_time > timedelta(minutes=5):
                        issues.append("长时间无状态更新，训练循环可能卡住")
                except:
                    pass
            
            # 检查轮次进度不匹配
            if total_epochs > 0 and current_epoch == 0 and progress > 25:
                issues.append("进度超过25%但轮次仍为0，进度更新机制有问题")
            
            if issues:
                for i, issue in enumerate(issues, 1):
                    print(f"   {i}. ⚠️ {issue}")
            else:
                print("   ✅ 未发现明显问题")
            
            return task_id
        
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        return None

def check_training_process_status():
    """检查训练进程状态"""
    print(f"\n💻 检查训练进程状态")
    print("-" * 40)
    
    try:
        import psutil
        
        # 查找Python进程
        python_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cpu_percent', 'memory_percent', 'create_time']):
            try:
                if 'python' in proc.info['name'].lower():
                    cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                    if 'app.py' in cmdline or 'deep_learning' in cmdline:
                        python_processes.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'cmdline': cmdline,
                            'cpu': proc.info['cpu_percent'],
                            'memory': proc.info['memory_percent'],
                            'create_time': proc.info['create_time']
                        })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        if python_processes:
            print(f"📊 找到 {len(python_processes)} 个相关Python进程:")
            for proc in python_processes:
                create_time = datetime.fromtimestamp(proc['create_time'])
                print(f"   PID {proc['pid']}: CPU {proc['cpu']:.1f}%, 内存 {proc['memory']:.1f}%")
                print(f"     启动时间: {create_time}")
                print(f"     命令行: {proc['cmdline'][:100]}...")
        else:
            print("⚠️ 没有找到相关的Python进程")
            
    except ImportError:
        print("⚠️ psutil未安装，无法检查进程状态")
    except Exception as e:
        print(f"❌ 检查进程失败: {e}")

def analyze_training_code_issues():
    """分析训练代码可能的问题"""
    print(f"\n🔧 分析训练代码可能的问题")
    print("-" * 40)
    
    print("📋 可能的问题点:")
    print("1. 训练循环未正确启动")
    print("   - _train_model方法中的训练循环可能卡在某个地方")
    print("   - 数据加载器可能有问题")
    print("   - 模型初始化可能失败")
    
    print("\n2. 进度更新机制问题")
    print("   - _update_task_progress调用可能失败")
    print("   - 数据库更新可能被阻塞")
    print("   - 异步任务可能没有正确执行")
    
    print("\n3. 异常处理不当")
    print("   - 训练过程中的异常可能被静默忽略")
    print("   - 错误信息可能没有正确记录")
    
    print("\n4. 资源问题")
    print("   - 内存不足导致训练卡住")
    print("   - GPU资源问题")
    print("   - 数据库锁定问题")

def suggest_debugging_steps():
    """建议调试步骤"""
    print(f"\n🛠️ 建议的调试步骤")
    print("-" * 40)
    
    print("1. 立即检查:")
    print("   - 查看应用程序日志文件")
    print("   - 检查系统资源使用情况")
    print("   - 验证数据库连接状态")
    
    print("\n2. 代码级调试:")
    print("   - 在训练循环中添加更多日志")
    print("   - 检查_train_model方法的执行流程")
    print("   - 验证进度更新函数是否被调用")
    
    print("\n3. 修复建议:")
    print("   - 添加训练循环超时机制")
    print("   - 改进异常处理和错误报告")
    print("   - 增加更频繁的进度更新")
    print("   - 添加训练状态心跳检测")

def create_training_monitor():
    """创建训练监控器"""
    print(f"\n📊 创建实时训练监控器")
    print("-" * 40)
    
    def monitor_training():
        """监控训练状态"""
        while True:
            try:
                conn = sqlite3.connect('trading_system.db')
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT id, progress, current_epoch, train_loss, updated_at
                    FROM training_tasks
                    WHERE status = 'running'
                    ORDER BY updated_at DESC
                    LIMIT 1
                ''')
                
                task = cursor.fetchone()
                conn.close()
                
                if task:
                    task_id, progress, epoch, loss, updated_at = task
                    current_time = datetime.now().strftime('%H:%M:%S')
                    print(f"[{current_time}] 任务 {task_id[:8]}... | 进度: {progress}% | 轮次: {epoch} | 损失: {loss} | 更新: {updated_at}")
                else:
                    print(f"[{datetime.now().strftime('%H:%M:%S')}] 没有运行中的训练任务")
                
                time.sleep(10)  # 每10秒检查一次
                
            except Exception as e:
                print(f"[{datetime.now().strftime('%H:%M:%S')}] 监控错误: {e}")
                time.sleep(10)
    
    # 启动监控线程
    monitor_thread = threading.Thread(target=monitor_training, daemon=True)
    monitor_thread.start()
    
    print("✅ 训练监控器已启动，每10秒检查一次状态")
    print("💡 按Ctrl+C停止监控")
    
    try:
        # 运行30秒的监控
        time.sleep(30)
    except KeyboardInterrupt:
        print("\n🛑 监控已停止")

def main():
    """主函数"""
    print("🔍 训练进度卡住问题深度诊断")
    print("=" * 60)
    
    # 1. 获取卡住任务详情
    stuck_task_id = get_stuck_training_details()
    
    # 2. 检查进程状态
    check_training_process_status()
    
    # 3. 分析代码问题
    analyze_training_code_issues()
    
    # 4. 建议调试步骤
    suggest_debugging_steps()
    
    # 5. 启动实时监控
    if stuck_task_id:
        print(f"\n🔄 启动实时监控...")
        create_training_monitor()
    
    print(f"\n📋 诊断完成！")
    print("下一步建议:")
    print("1. 检查deep_learning_service.py中的_train_model方法")
    print("2. 添加更多调试日志")
    print("3. 检查训练循环是否正确执行")

if __name__ == "__main__":
    main()
