#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试训练进度修复
不依赖Web服务，直接调用深度学习服务
"""

import sys
import os
import numpy as np
import time
from datetime import datetime

# 添加服务路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'services'))

def test_training_progress_directly():
    """直接测试训练进度"""
    print("🧪 直接测试训练进度修复")
    print("=" * 50)
    
    try:
        from services.deep_learning_service import DeepLearningService
        
        # 创建服务实例
        dl_service = DeepLearningService()
        print("✅ 深度学习服务创建成功")
        
        # 创建测试配置
        config = {
            'model_name': f'DIRECT_TEST_{int(time.time())}',
            'model_type': 'lstm',
            'symbol': 'XAUUSD',
            'timeframe': 'H1',
            'epochs': 3,  # 只训练3轮
            'batch_size': 4,  # 很小的批次
            'learning_rate': 0.01,
            'sequence_length': 10,  # 短序列
            'data_config': {
                'days': 5,  # 只用5天数据
                'validation_split': 0.2
            },
            'early_stopping': False,
            'save_checkpoints': False
        }
        
        print("📝 测试配置:")
        for key, value in config.items():
            if key != 'data_config':
                print(f"   {key}: {value}")
        
        # 启动训练
        print("\n🚀 启动训练...")
        result = dl_service.start_training(config)
        
        if result.get('success'):
            task_id = result.get('task_id')
            print(f"✅ 训练启动成功，任务ID: {task_id}")
            
            # 监控训练进度
            print("\n📊 监控训练进度...")
            monitor_training_directly(dl_service, task_id)
            
        else:
            print(f"❌ 训练启动失败: {result.get('error')}")
            
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("尝试简化测试...")
        test_training_components()
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def monitor_training_directly(dl_service, task_id, duration=60):
    """直接监控训练进度"""
    print(f"监控任务: {task_id[:8]}...")
    
    last_progress = -1
    last_epoch = -1
    progress_updates = 0
    
    for i in range(duration // 3):  # 每3秒检查一次
        try:
            # 获取训练进度
            progress_result = dl_service.get_training_progress(task_id)
            
            if progress_result.get('success'):
                progress_data = progress_result['progress']
                progress = progress_data.get('progress', 0)
                epoch = progress_data.get('epoch', 0)
                status = progress_data.get('status', 'unknown')
                train_loss = progress_data.get('train_loss', 0)
                
                elapsed = i * 3
                print(f"[{elapsed:3d}s] 进度: {progress:5.1f}%, 轮次: {epoch:2d}, 状态: {status}, 损失: {train_loss:.4f}")
                
                # 检查进度更新
                if progress != last_progress or epoch != last_epoch:
                    progress_updates += 1
                    print(f"       ✅ 进度更新 #{progress_updates}")
                    
                    # 如果有足够的更新，认为修复成功
                    if progress_updates >= 3:
                        print("       🎉 训练进度正常更新，修复成功！")
                        return True
                
                last_progress = progress
                last_epoch = epoch
                
                # 检查训练是否完成
                if status in ['completed', 'failed', 'stopped']:
                    print(f"       🏁 训练结束: {status}")
                    return status == 'completed'
                    
            else:
                print(f"[{i*3:3d}s] ❌ 获取进度失败: {progress_result.get('error')}")
                
        except Exception as e:
            print(f"[{i*3:3d}s] ❌ 监控异常: {e}")
        
        time.sleep(3)
    
    print("⏰ 监控超时")
    return False

def test_training_components():
    """测试训练组件"""
    print("\n🔧 测试训练组件")
    print("-" * 30)
    
    try:
        import torch
        import torch.nn as nn
        
        print("✅ PyTorch导入成功")
        
        # 测试简单的训练循环
        print("🧪 测试简单训练循环...")
        
        # 创建简单数据
        X = torch.randn(100, 10, 5)  # 100个样本，10个时间步，5个特征
        y = torch.randint(0, 2, (100,)).float()  # 二分类标签
        
        # 创建简单模型
        class SimpleModel(nn.Module):
            def __init__(self):
                super().__init__()
                self.lstm = nn.LSTM(5, 10, batch_first=True)
                self.fc = nn.Linear(10, 1)
            
            def forward(self, x):
                out, _ = self.lstm(x)
                out = self.fc(out[:, -1, :])
                return out.squeeze()
        
        model = SimpleModel()
        criterion = nn.BCEWithLogitsLoss()
        optimizer = torch.optim.Adam(model.parameters(), lr=0.01)
        
        print("✅ 模型创建成功")
        
        # 测试训练循环
        print("🔄 测试训练循环...")
        
        for epoch in range(3):
            model.train()
            total_loss = 0
            
            # 简单的批次循环
            batch_size = 10
            for i in range(0, len(X), batch_size):
                batch_X = X[i:i+batch_size]
                batch_y = y[i:i+batch_size]
                
                optimizer.zero_grad()
                outputs = model(batch_X)
                loss = criterion(outputs, batch_y)
                loss.backward()
                optimizer.step()
                
                total_loss += loss.item()
                
                print(f"   Epoch {epoch+1}, Batch {i//batch_size+1}: 损失 {loss.item():.4f}")
            
            avg_loss = total_loss / (len(X) // batch_size)
            print(f"✅ Epoch {epoch+1} 完成，平均损失: {avg_loss:.4f}")
        
        print("✅ 简单训练循环测试成功")
        
    except Exception as e:
        print(f"❌ 组件测试失败: {e}")
        import traceback
        traceback.print_exc()

def check_current_training_status():
    """检查当前训练状态"""
    print("\n🔍 检查当前训练状态")
    print("-" * 30)
    
    try:
        import sqlite3
        
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, status, progress, current_epoch, train_loss, updated_at
            FROM training_tasks
            WHERE status = 'running'
            ORDER BY updated_at DESC
            LIMIT 3
        ''')
        
        tasks = cursor.fetchall()
        conn.close()
        
        if tasks:
            print(f"📊 找到 {len(tasks)} 个运行中的任务:")
            for task in tasks:
                task_id, status, progress, epoch, loss, updated_at = task
                print(f"   {task_id[:8]}... | {status} | {progress}% | 轮次{epoch} | 损失{loss} | {updated_at}")
        else:
            print("ℹ️ 没有运行中的训练任务")
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")

def main():
    """主函数"""
    print("🧪 训练进度修复直接测试")
    print("=" * 60)
    
    # 1. 检查当前状态
    check_current_training_status()
    
    # 2. 直接测试训练
    test_training_progress_directly()
    
    print("\n📋 测试总结:")
    print("如果看到训练进度正常更新，说明修复成功")
    print("如果训练仍然卡住，需要进一步调试")

if __name__ == "__main__":
    main()
