#!/usr/bin/env python3
"""
深度学习服务 - 处理模型训练、管理和推理
"""

import os
import json
import sqlite3
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import logging
import threading
import time
import uuid
import hashlib
import pickle
import psutil
import signal

# GPU相关导入
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import Dataset, DataLoader
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

try:
    import tensorflow as tf
    TF_AVAILABLE = True
except ImportError:
    TF_AVAILABLE = False

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TradingDataset(Dataset):
    """交易数据集类"""
    
    def __init__(self, sequences, targets):
        self.sequences = torch.FloatTensor(sequences)
        self.targets = torch.FloatTensor(targets)
    
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        return self.sequences[idx], self.targets[idx]

class LSTMModel(nn.Module):
    """LSTM交易预测模型"""

    def __init__(self, input_size, hidden_size, num_layers, output_size, dropout=0.2):
        super(LSTMModel, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers

        self.lstm = nn.LSTM(input_size, hidden_size, num_layers,
                           batch_first=True, dropout=dropout)
        self.dropout = nn.Dropout(dropout)
        self.fc = nn.Linear(hidden_size, output_size)

    def forward(self, x):
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)
        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)

        out, _ = self.lstm(x, (h0, c0))
        out = self.dropout(out[:, -1, :])
        out = self.fc(out)
        return out

class GRUModel(nn.Module):
    """GRU交易预测模型"""

    def __init__(self, input_size, hidden_size, num_layers, output_size, dropout=0.2):
        super(GRUModel, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers

        self.gru = nn.GRU(input_size, hidden_size, num_layers,
                         batch_first=True, dropout=dropout)
        self.dropout = nn.Dropout(dropout)
        self.fc = nn.Linear(hidden_size, output_size)

    def forward(self, x):
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)

        out, _ = self.gru(x, h0)
        out = self.dropout(out[:, -1, :])
        out = self.fc(out)
        return out

class TransformerModel(nn.Module):
    """Transformer交易预测模型"""

    def __init__(self, input_size, hidden_size, num_layers, output_size, dropout=0.2, nhead=8):
        super(TransformerModel, self).__init__()
        self.input_size = input_size
        self.hidden_size = hidden_size

        # 输入投影
        self.input_projection = nn.Linear(input_size, hidden_size)

        # 位置编码
        self.pos_encoding = nn.Parameter(torch.randn(1000, hidden_size))

        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=hidden_size,
            nhead=nhead,
            dim_feedforward=hidden_size * 4,
            dropout=dropout,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)

        # 输出层
        self.dropout = nn.Dropout(dropout)
        self.fc = nn.Linear(hidden_size, output_size)

    def forward(self, x):
        seq_len = x.size(1)

        # 输入投影
        x = self.input_projection(x)

        # 添加位置编码
        x = x + self.pos_encoding[:seq_len, :].unsqueeze(0)

        # Transformer编码
        out = self.transformer(x)

        # 取最后一个时间步
        out = self.dropout(out[:, -1, :])
        out = self.fc(out)
        return out

class CNNLSTMModel(nn.Module):
    """CNN-LSTM混合模型"""

    def __init__(self, input_size, hidden_size, num_layers, output_size, dropout=0.2):
        super(CNNLSTMModel, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers

        # CNN特征提取
        self.conv1 = nn.Conv1d(input_size, hidden_size//2, kernel_size=3, padding=1)
        self.conv2 = nn.Conv1d(hidden_size//2, hidden_size//2, kernel_size=3, padding=1)
        self.pool = nn.MaxPool1d(2)

        # LSTM序列建模
        self.lstm = nn.LSTM(hidden_size//2, hidden_size, num_layers,
                           batch_first=True, dropout=dropout)

        # 输出层
        self.dropout = nn.Dropout(dropout)
        self.fc = nn.Linear(hidden_size, output_size)

    def forward(self, x):
        # CNN特征提取 (batch, seq, features) -> (batch, features, seq)
        x = x.transpose(1, 2)
        x = torch.relu(self.conv1(x))
        x = torch.relu(self.conv2(x))

        # 如果序列长度足够，进行池化
        if x.size(2) >= 2:
            x = self.pool(x)

        # 转回LSTM格式 (batch, features, seq) -> (batch, seq, features)
        x = x.transpose(1, 2)

        # LSTM处理
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)
        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)

        out, _ = self.lstm(x, (h0, c0))
        out = self.dropout(out[:, -1, :])
        out = self.fc(out)
        return out

class AttentionLSTMModel(nn.Module):
    """注意力LSTM模型"""

    def __init__(self, input_size, hidden_size, num_layers, output_size, dropout=0.2):
        super(AttentionLSTMModel, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers

        # LSTM层
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers,
                           batch_first=True, dropout=dropout)

        # 注意力机制
        self.attention = nn.MultiheadAttention(hidden_size, num_heads=8,
                                             dropout=dropout, batch_first=True)

        # 输出层
        self.dropout = nn.Dropout(dropout)
        self.fc = nn.Linear(hidden_size, output_size)

    def forward(self, x):
        # LSTM处理
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)
        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)

        lstm_out, _ = self.lstm(x, (h0, c0))

        # 自注意力
        attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)

        # 取最后一个时间步
        out = self.dropout(attn_out[:, -1, :])
        out = self.fc(out)
        return out

class DeepLearningService:
    """深度学习服务"""
    
    def __init__(self):
        self.db_path = 'trading_system.db'
        self.models_path = 'deep_learning_models'
        self.cache_path = 'data_cache'  # 数据缓存目录
        self.training_tasks = {}  # 存储训练任务状态
        self.training_control = {}  # 存储训练控制状态 {task_id: {'stop': False, 'pause': False}}
        self.system_monitor = {}  # 系统监控状态

        # 确保目录存在
        for path in [self.models_path, self.cache_path]:
            if not os.path.exists(path):
                os.makedirs(path)

        # 检测GPU
        self.device = self._detect_device()
        logger.info(f"🔧 深度学习服务使用设备: {self.device}")

        # 初始化数据库表
        self._init_database()
        self._ensure_trading_tables()

        # 启动系统监控
        self._start_system_monitor()
    
    def _detect_device(self):
        """检测可用设备并验证GPU可用性"""
        if TORCH_AVAILABLE and torch.cuda.is_available():
            try:
                # 验证GPU实际可用
                torch.zeros(1).cuda()  # 测试GPU操作
                logger.info(f"✅ GPU验证通过: {torch.cuda.get_device_name(0)}")
                return torch.device('cuda')
            except Exception as e:
                logger.error(f"❌ GPU初始化失败: {str(e)}")
                logger.warning("⚠️ 将回退到CPU模式")
        return torch.device('cpu')

    def _start_system_monitor(self):
        """启动系统监控"""
        def monitor_loop():
            while True:
                try:
                    # 监控系统资源
                    cpu_percent = psutil.cpu_percent(interval=1)
                    memory = psutil.virtual_memory()

                    # GPU监控
                    gpu_info = {}
                    if self.device.type == 'cuda' and TORCH_AVAILABLE:
                        try:
                            gpu_info = {
                                'memory_allocated': torch.cuda.memory_allocated() / 1024**3,  # GB
                                'memory_reserved': torch.cuda.memory_reserved() / 1024**3,   # GB
                                'utilization': torch.cuda.utilization() if hasattr(torch.cuda, 'utilization') else 0
                            }
                        except:
                            pass

                    self.system_monitor = {
                        'timestamp': datetime.now(),
                        'cpu_percent': cpu_percent,
                        'memory_percent': memory.percent,
                        'memory_available_gb': memory.available / 1024**3,
                        'gpu_info': gpu_info
                    }

                    # 检查训练任务状态
                    self._check_training_health()

                    time.sleep(10)  # 每10秒更新一次
                except Exception as e:
                    logger.error(f"系统监控错误: {e}")
                    time.sleep(30)

        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()

    def _check_training_health(self):
        """检查训练任务健康状态"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 查找长时间无更新的运行中任务
            cursor.execute('''
                SELECT id, model_id, progress, current_epoch, updated_at
                FROM training_tasks
                WHERE status = 'running'
                AND datetime(updated_at) < datetime('now', '-5 minutes')
            ''')

            stuck_tasks = cursor.fetchall()

            for task_id, model_id, progress, current_epoch, updated_at in stuck_tasks:
                logger.warning(f"⚠️ 检测到可能卡死的训练任务: {task_id}")
                logger.warning(f"   进度: {progress}%, 当前轮次: {current_epoch}")
                logger.warning(f"   最后更新: {updated_at}")

                # 发送告警
                self._send_training_alert(task_id, "training_stuck", {
                    'progress': progress,
                    'current_epoch': current_epoch,
                    'last_update': updated_at,
                    'stuck_duration': '5+ minutes'
                })

            conn.close()

        except Exception as e:
            logger.error(f"训练健康检查失败: {e}")

    def _send_training_alert(self, task_id: str, alert_type: str, details: Dict[str, Any]):
        """发送训练告警"""
        try:
            alert_message = {
                'timestamp': datetime.now().isoformat(),
                'task_id': task_id,
                'alert_type': alert_type,
                'details': details,
                'system_info': self.system_monitor
            }

            # 记录告警到日志
            logger.warning(f"🚨 训练告警 [{alert_type}]: {task_id}")
            logger.warning(f"   详情: {details}")

            # 这里可以添加更多告警方式，如邮件、短信等
            # 目前只记录到日志

        except Exception as e:
            logger.error(f"发送告警失败: {e}")

    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            # 基础系统信息
            status = {
                'timestamp': datetime.now().isoformat(),
                'system_monitor': self.system_monitor,
                'device': str(self.device),
                'torch_available': TORCH_AVAILABLE,
                'tensorflow_available': TF_AVAILABLE
            }

            # 训练任务统计
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT status, COUNT(*)
                FROM training_tasks
                GROUP BY status
            ''')
            task_stats = dict(cursor.fetchall())
            status['task_statistics'] = task_stats

            # 活跃训练任务
            cursor.execute('''
                SELECT id, progress, current_epoch, updated_at
                FROM training_tasks
                WHERE status = 'running'
                ORDER BY updated_at DESC
                LIMIT 5
            ''')
            active_tasks = cursor.fetchall()
            status['active_tasks'] = [
                {
                    'task_id': task_id,
                    'progress': progress,
                    'current_epoch': current_epoch,
                    'last_update': updated_at
                }
                for task_id, progress, current_epoch, updated_at in active_tasks
            ]

            conn.close()
            return status

        except Exception as e:
            logger.error(f"获取系统状态失败: {e}")
            return {'error': str(e), 'timestamp': datetime.now().isoformat()}

    def _generate_cache_key(self, config: Dict[str, Any]) -> str:
        """生成数据缓存键"""
        cache_data = {
            'symbol': config.get('symbol'),
            'timeframe': config.get('timeframe'),
            'data_config': config.get('data_config', {}),
            'sequence_length': config.get('sequence_length', 60)
        }
        cache_str = json.dumps(cache_data, sort_keys=True)
        return hashlib.md5(cache_str.encode()).hexdigest()

    def _save_data_cache(self, cache_key: str, data: Dict[str, Any]):
        """保存数据到缓存"""
        try:
            cache_file = os.path.join(self.cache_path, f"{cache_key}.pkl")
            with open(cache_file, 'wb') as f:
                pickle.dump(data, f)
            logger.info(f"✅ 数据已缓存: {cache_key}")
        except Exception as e:
            logger.error(f"❌ 保存缓存失败: {e}")

    def _load_data_cache(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """从缓存加载数据"""
        try:
            cache_file = os.path.join(self.cache_path, f"{cache_key}.pkl")
            if os.path.exists(cache_file):
                # 检查缓存文件是否过期（24小时）
                file_time = os.path.getmtime(cache_file)
                if time.time() - file_time < 24 * 3600:
                    with open(cache_file, 'rb') as f:
                        data = pickle.load(f)
                    logger.info(f"✅ 从缓存加载数据: {cache_key}")
                    return data
                else:
                    logger.info(f"⚠️ 缓存已过期: {cache_key}")
                    os.remove(cache_file)
            return None
        except Exception as e:
            logger.error(f"❌ 加载缓存失败: {e}")
            return None

    def _validate_data_integrity(self, data: Dict[str, Any]) -> bool:
        """验证数据完整性"""
        try:
            required_keys = ['X_train', 'X_val', 'y_train', 'y_val', 'feature_names', 'data_info']

            # 检查必需的键
            for key in required_keys:
                if key not in data:
                    logger.error(f"❌ 缺少必需的数据键: {key}")
                    return False

            # 检查数据形状
            X_train, X_val, y_train, y_val = data['X_train'], data['X_val'], data['y_train'], data['y_val']

            if len(X_train) == 0 or len(X_val) == 0:
                logger.error("❌ 训练或验证数据为空")
                return False

            if X_train.shape[0] != y_train.shape[0]:
                logger.error("❌ 训练数据和标签数量不匹配")
                return False

            if X_val.shape[0] != y_val.shape[0]:
                logger.error("❌ 验证数据和标签数量不匹配")
                return False

            # 检查数据质量
            if np.isnan(X_train).any() or np.isnan(X_val).any():
                logger.error("❌ 数据包含NaN值")
                return False

            if np.isinf(X_train).any() or np.isinf(X_val).any():
                logger.error("❌ 数据包含无穷值")
                return False

            logger.info("✅ 数据完整性验证通过")
            return True

        except Exception as e:
            logger.error(f"❌ 数据验证失败: {e}")
            return False
    
    def _init_database(self):
        """初始化数据库表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建深度学习模型表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS deep_learning_models (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    model_type TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    timeframe TEXT NOT NULL,
                    model_path TEXT,
                    config TEXT,
                    training_history TEXT,
                    performance_metrics TEXT,
                    status TEXT DEFAULT 'training',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    completed_at TIMESTAMP,
                    user_id INTEGER,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            # 创建训练任务表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS training_tasks (
                    id TEXT PRIMARY KEY,
                    model_id TEXT,
                    status TEXT DEFAULT 'pending',
                    progress REAL DEFAULT 0,
                    current_epoch INTEGER DEFAULT 0,
                    total_epochs INTEGER,
                    train_loss REAL,
                    val_loss REAL,
                    best_loss REAL,
                    logs TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    started_at TIMESTAMP,
                    completed_at TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (model_id) REFERENCES deep_learning_models (id)
                )
            ''')

            # 创建自动交易会话表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS auto_trading_sessions (
                    id TEXT PRIMARY KEY,
                    session_id TEXT NOT NULL,
                    user_id INTEGER NOT NULL,
                    model_id TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    timeframe TEXT NOT NULL,
                    status TEXT DEFAULT 'active',
                    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    end_time TIMESTAMP,
                    total_trades INTEGER DEFAULT 0,
                    total_profit REAL DEFAULT 0.0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (model_id) REFERENCES deep_learning_models (id)
                )
            ''')

            conn.commit()
            conn.close()
            
            logger.info("✅ 深度学习数据库表初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 初始化数据库失败: {e}")
    
    def get_gpu_status(self) -> Dict[str, Any]:
        """获取GPU状态"""
        try:
            import subprocess
            import random

            status = {
                'gpu_available': False,
                'gpu_name': None,
                'memory_total': 0,
                'memory_used': 0,
                'memory_free': 0,
                'memory_usage_percent': 0,
                'gpu_utilization': 0,
                'temperature': 0,
                'power_usage': 0,
                'cuda_version': None,
                'pytorch_version': None,
                'tensorflow_version': None
            }

            if TORCH_AVAILABLE:
                status['pytorch_version'] = torch.__version__

                if torch.cuda.is_available():
                    status['gpu_available'] = True
                    status['gpu_name'] = torch.cuda.get_device_name(0)
                    status['cuda_version'] = torch.version.cuda

                    # GPU内存信息
                    memory_total = torch.cuda.get_device_properties(0).total_memory / 1024**3
                    memory_allocated = torch.cuda.memory_allocated(0) / 1024**3
                    memory_reserved = torch.cuda.memory_reserved(0) / 1024**3

                    status['memory_total'] = memory_total
                    status['memory_used'] = memory_allocated
                    status['memory_free'] = memory_total - memory_reserved
                    status['memory_usage_percent'] = (memory_allocated / memory_total * 100) if memory_total > 0 else 0

                    # 尝试获取更详细的GPU信息
                    try:
                        # 使用nvidia-smi获取GPU使用率、温度、功耗
                        result = subprocess.run([
                            'nvidia-smi',
                            '--query-gpu=utilization.gpu,temperature.gpu,power.draw',
                            '--format=csv,noheader,nounits'
                        ], capture_output=True, text=True, timeout=5)

                        if result.returncode == 0:
                            gpu_info = result.stdout.strip().split(', ')
                            if len(gpu_info) >= 3:
                                status['gpu_utilization'] = float(gpu_info[0])
                                status['temperature'] = float(gpu_info[1])
                                status['power_usage'] = float(gpu_info[2])
                        else:
                            # 如果nvidia-smi失败，返回未知状态
                            status['gpu_utilization'] = 0
                            status['temperature'] = 0
                            status['power_usage'] = 0
                            status['status'] = 'nvidia-smi命令执行失败'

                    except (subprocess.TimeoutExpired, FileNotFoundError, Exception) as e:
                        # nvidia-smi不可用时返回错误状态
                        status['gpu_utilization'] = 0
                        status['temperature'] = 0
                        status['power_usage'] = 0
                        status['status'] = f'nvidia-smi不可用: {e}'

            if TF_AVAILABLE:
                status['tensorflow_version'] = tf.__version__

                gpus = tf.config.experimental.list_physical_devices('GPU')
                if gpus and not status['gpu_available']:
                    status['gpu_available'] = True
                    status['gpu_name'] = f"TensorFlow GPU ({len(gpus)} devices)"

            return status

        except Exception as e:
            logger.error(f"❌ 获取GPU状态失败: {e}")
            return {'gpu_available': False, 'error': str(e)}
    
    def start_training(self, config: Dict[str, Any], user_id: int) -> Dict[str, Any]:
        """启动模型训练"""
        try:
            # 生成唯一ID
            model_id = str(uuid.uuid4())
            task_id = str(uuid.uuid4())
            
            # 保存模型配置到数据库
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO deep_learning_models 
                (id, name, model_type, symbol, timeframe, config, status, user_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                model_id,
                config['model_name'],
                config['model_type'],
                config['symbol'],
                config['timeframe'],
                json.dumps(config),
                'training',
                user_id
            ))
            
            cursor.execute('''
                INSERT INTO training_tasks 
                (id, model_id, status, total_epochs)
                VALUES (?, ?, ?, ?)
            ''', (task_id, model_id, 'pending', config['epochs']))
            
            conn.commit()
            conn.close()
            
            # 初始化训练控制状态
            self.training_control[task_id] = {'stop': False, 'pause': False}

            # 标记为自动训练模式（数据准备完成后自动开始训练）
            self.training_control[task_id]['auto_train'] = True

            # 在后台线程中启动数据准备
            data_prep_thread = threading.Thread(
                target=self._prepare_data_async,
                args=(model_id, task_id, config)
            )
            data_prep_thread.daemon = True
            data_prep_thread.start()

            logger.info(f"✅ 训练任务已启动: {task_id}")

            return {
                'success': True,
                'task_id': task_id,
                'model_id': model_id,
                'message': '训练任务已启动'
            }
            
        except Exception as e:
            logger.error(f"❌ 启动训练失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def start_data_preparation(self, config: Dict[str, Any], user_id: int) -> Dict[str, Any]:
        """启动数据准备阶段"""
        try:
            # 生成唯一ID
            model_id = str(uuid.uuid4())
            task_id = str(uuid.uuid4())

            logger.info(f"📊 启动数据准备任务: {task_id}")
            logger.info(f"📋 模型ID: {model_id}")
            logger.info(f"⚙️ 配置: {config}")

            # 保存模型配置到数据库
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO deep_learning_models
                (id, name, model_type, symbol, timeframe, config, status, user_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                model_id,
                config['model_name'],
                config['model_type'],
                config['symbol'],
                config['timeframe'],
                json.dumps(config),
                'data_preparation',  # 初始状态为数据准备
                user_id
            ))

            cursor.execute('''
                INSERT INTO training_tasks
                (id, model_id, status, total_epochs)
                VALUES (?, ?, ?, ?)
            ''', (task_id, model_id, 'data_preparation', config['epochs']))

            conn.commit()
            conn.close()

            # 初始化训练控制状态
            self.training_control[task_id] = {'stop': False, 'pause': False}

            # 在后台线程中启动数据准备
            data_prep_thread = threading.Thread(
                target=self._prepare_data_async,
                args=(model_id, task_id, config)
            )
            data_prep_thread.daemon = True
            data_prep_thread.start()

            logger.info(f"✅ 数据准备任务已启动: {task_id}")

            return {
                'success': True,
                'task_id': task_id,
                'model_id': model_id,
                'message': '数据准备任务已启动',
                'stage': 'data_preparation'
            }

        except Exception as e:
            logger.error(f"❌ 启动数据准备失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def start_model_training(self, task_id: str) -> Dict[str, Any]:
        """启动模型训练阶段（数据准备完成后）"""
        try:
            logger.info(f"🚀 启动模型训练: {task_id}")

            # 检查任务状态
            task_info = self.get_training_progress(task_id)
            if not task_info.get('success'):
                return {'success': False, 'error': '任务不存在'}

            task_data = task_info['progress']
            if task_data['status'] != 'data_ready':
                return {'success': False, 'error': f'任务状态不正确: {task_data["status"]}，需要先完成数据准备'}

            # 获取模型ID和配置
            model_id = task_data['model_id']

            # 从模型表获取配置
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('''
                SELECT config FROM deep_learning_models
                WHERE id = ?
            ''', (model_id,))

            model_result = cursor.fetchone()
            conn.close()

            if not model_result:
                return {'success': False, 'error': '模型配置不存在'}

            config_str = model_result[0]
            if isinstance(config_str, str):
                config = json.loads(config_str)
            else:
                config = config_str

            # 更新任务状态为训练中
            self._update_task_status(task_id, 'running',
                logs=json.dumps({
                    'stage': 'model_training',
                    'message': '开始模型训练阶段'
                }))

            # 在后台线程中启动模型训练
            training_thread = threading.Thread(
                target=self._train_model_async,
                args=(model_id, task_id, config, True)  # skip_data_prep=True
            )
            training_thread.daemon = True
            training_thread.start()

            return {
                'success': True,
                'task_id': task_id,
                'message': '模型训练已启动',
                'stage': 'model_training'
            }

        except Exception as e:
            logger.error(f"❌ 启动模型训练失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _prepare_data_async(self, model_id: str, task_id: str, config: Dict[str, Any]):
        """异步数据准备"""
        try:
            logger.info(f"📊 开始数据准备: {model_id}")

            # 添加数据准备超时检查
            import time
            data_prep_start_time = time.time()
            max_data_prep_time = 1800  # 30分钟最大数据准备时间

            def check_data_prep_timeout():
                if time.time() - data_prep_start_time > max_data_prep_time:
                    logger.error("❌ 数据准备超时，自动停止")
                    raise TimeoutError("数据准备超时")

            # 更新状态为数据准备中
            self._update_task_status(task_id, 'data_preparation',
                logs=json.dumps({
                    'stage': 'data_preparation',
                    'message': '开始数据准备阶段'
                }))
            self._update_task_progress(task_id, 5, 0, 0, 0)

            # 准备数据
            logger.info("📊 获取和处理训练数据...")
            check_data_prep_timeout()  # 检查超时

            X_train, X_val, y_train, y_val = self._prepare_training_data(config, task_id)

            check_data_prep_timeout()  # 再次检查超时

            if X_train is None:
                raise Exception("数据准备失败：无法获取MT5历史数据，请检查MT5连接和数据权限")

            # 数据准备完成，更新状态
            self._update_task_status(task_id, 'data_ready',
                logs=json.dumps({
                    'stage': 'data_ready',
                    'message': '数据准备完成，可以开始模型训练',
                    'data_info': {
                        'X_train_shape': list(X_train.shape),
                        'X_val_shape': list(X_val.shape),
                        'y_train_shape': list(y_train.shape),
                        'y_val_shape': list(y_val.shape)
                    }
                }))
            self._update_task_progress(task_id, 100, 0, 0, 0)  # 数据准备100%完成

            # 更新模型状态
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE deep_learning_models
                SET status = 'data_ready'
                WHERE id = ?
            ''', (model_id,))
            conn.commit()
            conn.close()

            logger.info(f"✅ 数据准备完成: {model_id}")

            # 检查是否需要自动开始训练
            if self.training_control.get(task_id, {}).get('auto_train', False):
                logger.info("🚀 自动开始模型训练...")
                # 添加短暂延迟确保状态更新完成
                import time
                time.sleep(0.5)

                # 再次确认状态
                current_status = self.get_training_progress(task_id)
                if current_status.get('success') and current_status['progress']['status'] == 'data_ready':
                    result = self.start_model_training(task_id)
                    if not result.get('success'):
                        logger.warning(f"⚠️ 自动训练启动失败: {result.get('error')}")
                else:
                    logger.warning(f"⚠️ 自动训练跳过，状态不正确: {current_status.get('progress', {}).get('status', 'unknown')}")

        except Exception as e:
            logger.error(f"❌ 数据准备失败: {e}")
            self._update_task_status(task_id, 'failed',
                logs=json.dumps({
                    'stage': 'data_preparation_failed',
                    'message': f'数据准备失败: {str(e)}'
                }))

            # 更新模型状态
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE deep_learning_models
                    SET status = 'failed'
                    WHERE id = ?
                ''', (model_id,))
                conn.commit()
                conn.close()
            except:
                pass

    def _train_model_async(self, model_id: str, task_id: str, config: Dict[str, Any], skip_data_prep: bool = False):
        """异步训练模型（增加进度回调）"""
        try:
            # 添加训练总超时检查
            self.training_start_time = time.time()
            self.max_training_time = 3600 * 6  # 6小时最大训练时间

            logger.info(f"🚀 开始训练模型: {model_id}")

            # 新增：训练开始回调
            self._training_callback(task_id, "start", 0, 0, 0)

            # 更新任务状态为运行中
            self._update_task_status(task_id, 'running', started_at=datetime.now())
            self._update_task_progress(task_id, 5, 0, 0, 0)  # 初始进度5%

            # 准备数据（根据skip_data_prep参数决定）
            if skip_data_prep:
                logger.info("📊 跳过数据准备，从缓存加载数据...")
                # 从缓存加载数据
                cache_key = self._generate_cache_key(config)
                cached_data = self._load_data_cache(cache_key)

                if cached_data and self._validate_data_integrity(cached_data):
                    X_train = cached_data['X_train']
                    X_val = cached_data['X_val']
                    y_train = cached_data['y_train']
                    y_val = cached_data['y_val']
                    logger.info("✅ 成功从缓存加载训练数据")
                    self._update_task_progress(task_id, 25, 0, 0, 0)
                else:
                    logger.warning("⚠️ 缓存数据不可用，重新准备数据...")
                    X_train, X_val, y_train, y_val = self._prepare_training_data(config, task_id)
                    if X_train is None:
                        raise Exception("数据准备失败：无法获取MT5历史数据，请检查MT5连接和数据权限")
                    self._update_task_progress(task_id, 25, 0, 0, 0)
            else:
                # 数据准备阶段需要检查MT5连接
                logger.info("🔍 数据准备前检查MT5连接状态...")
                if not self._ensure_mt5_connection_for_training():
                    raise Exception("MT5连接检查失败，无法进行数据准备")

                logger.info("📊 准备训练数据...")
                self._update_task_progress(task_id, 10, 0, 0, 0)  # 数据准备开始10%

                X_train, X_val, y_train, y_val = self._prepare_training_data(config, task_id)

                if X_train is None:
                    raise Exception("数据准备失败：无法获取MT5历史数据，请检查MT5连接和数据权限")

                self._update_task_progress(task_id, 25, 0, 0, 0)  # 数据准备完成25%

            # 创建模型
            logger.info("🧠 创建模型...")
            model = self._create_model(config, X_train.shape[-1])
            self._update_task_progress(task_id, 25, 0, 0, 0)  # 模型创建完成25%

            # 训练模型
            logger.info("🏋️ 开始训练...")

            # 添加训练前的超时检查
            training_start_time = time.time()
            max_training_wait = 300  # 5分钟超时

            try:
                training_history = self._train_model(
                    model, X_train, X_val, y_train, y_val, config, task_id
                )
            except Exception as e:
                training_duration = time.time() - training_start_time
                logger.error(f"❌ 训练失败 (耗时: {training_duration:.1f}秒): {e}")

                # 更新任务状态为失败
                self._update_task_status(task_id, 'failed',
                    logs=json.dumps({
                        'stage': 'training_failed',
                        'message': f'训练失败: {str(e)}',
                        'error': str(e),
                        'training_duration': training_duration
                    }))

                raise e

            # 保存模型
            logger.info("💾 保存模型...")
            model_path = os.path.join(self.models_path, f"{model_id}.pt")
            torch.save(model.state_dict(), model_path)

            # 计算性能指标
            logger.info("📊 计算性能指标...")
            performance = self._evaluate_model(model, X_val, y_val)

            # 收集完整的数据信息 - 修复日期获取逻辑
            data_config = config.get('data_config', {})
            date_range = self._get_date_range_info(data_config)

            data_info = {
                'start_date': date_range.get('start_date'),
                'end_date': date_range.get('end_date'),
                'total_samples': len(X_train) + len(X_val),
                'training_samples': len(X_train),
                'validation_samples': len(X_val),
                'features_used': config.get('features', ['open', 'high', 'low', 'close', 'volume']),
                'data_quality': 'good' if len(X_train) + len(X_val) > 10000 else 'fair' if len(X_train) + len(X_val) > 5000 else 'poor',
                'sequence_length': config.get('sequence_length', 60),
                'validation_split': 0.2,
                'feature_count': X_train.shape[-1] if len(X_train.shape) > 1 else 1
            }

            logger.info(f"📅 保存的数据信息 - 开始: {data_info['start_date']}, 结束: {data_info['end_date']}")

            # 更新数据库
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE deep_learning_models
                SET model_path = ?, training_history = ?, performance_metrics = ?,
                    status = 'completed', completed_at = ?
                WHERE id = ?
            ''', (
                model_path,
                json.dumps(training_history),
                json.dumps(performance),
                datetime.now(),
                model_id
            ))

            # 更新训练任务日志，包含完整的数据信息
            cursor.execute('''
                UPDATE training_tasks
                SET logs = ?
                WHERE id = ?
            ''', (
                json.dumps({
                    'stage': 'completed',
                    'message': '训练完成',
                    'data_info': data_info,
                    'training_info': {
                        'total_samples': data_info['total_samples'],
                        'training_samples': data_info['training_samples'],
                        'validation_samples': data_info['validation_samples'],
                        'feature_count': data_info['feature_count']
                    },
                    'performance': performance
                }),
                task_id
            ))

            # 只有在所有轮次完成后才设置为100%
            cursor.execute('''
                UPDATE training_tasks
                SET status = 'completed', progress = 100, completed_at = ?
                WHERE id = ? AND current_epoch >= total_epochs
            ''', (datetime.now(), task_id))

            # 如果训练提前停止，保持当前进度
            cursor.execute('''
                UPDATE training_tasks
                SET status = 'completed', completed_at = ?
                WHERE id = ? AND current_epoch < total_epochs
            ''', (datetime.now(), task_id))

            conn.commit()
            conn.close()

            logger.info(f"✅ 模型训练完成: {model_id}")

        except Exception as e:
            import traceback
            error_msg = str(e) if str(e) else "未知训练错误"
            full_traceback = traceback.format_exc()

            logger.error(f"❌ 模型训练失败: {error_msg}")
            logger.error(f"完整错误堆栈: {full_traceback}")

            # 更新失败状态，包含详细错误信息
            detailed_error = f"{error_msg}\n\n堆栈跟踪:\n{full_traceback}"
            self._update_task_status(task_id, 'failed', error=detailed_error)

            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE deep_learning_models
                    SET status = 'failed'
                    WHERE id = ?
                ''', (model_id,))
                conn.commit()
                conn.close()
            except Exception as db_error:
                logger.error(f"❌ 更新数据库失败状态时出错: {db_error}")

        finally:
            # 清理训练控制状态
            if task_id in self.training_control:
                del self.training_control[task_id]
                logger.info(f"🧹 清理训练控制状态: {task_id}")

    def _ensure_mt5_connection_for_training(self) -> bool:
        """确保MT5连接正常，用于模型训练"""
        try:
            from services.mt5_service import mt5_service
            import time

            logger.info("🔍 检查MT5连接状态...")

            # 检查当前连接状态
            if mt5_service.is_connected():
                logger.info("✅ MT5已连接")

                # 测试数据获取
                test_data = mt5_service.get_historical_data("XAUUSD", "H1", 10)
                if test_data and len(test_data) > 0:
                    logger.info("✅ MT5数据获取测试通过")
                    return True
                else:
                    logger.warning("⚠️ MT5数据获取测试失败，尝试重新连接...")
            else:
                logger.warning("⚠️ MT5未连接，尝试连接...")

            # 尝试连接MT5
            logger.info("🔄 尝试连接MT5...")
            if mt5_service.connect():
                logger.info("✅ MT5连接成功")

                # 再次测试数据获取
                test_data = mt5_service.get_historical_data("XAUUSD", "H1", 10)
                if test_data and len(test_data) > 0:
                    logger.info("✅ MT5连接和数据获取验证通过")
                    return True
                else:
                    logger.error("❌ MT5连接成功但数据获取失败")
                    return False
            else:
                logger.error("❌ MT5连接失败")
                return False

        except Exception as e:
            logger.error(f"❌ MT5连接检查异常: {e}")
            return False

    def _prepare_training_data(self, config: Dict[str, Any], task_id: str = None) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """准备训练数据（增强版本，包含缓存和验证机制）"""
        try:
            # 解析数据配置
            data_config = config.get('data_config', {})
            days = self._calculate_training_days(data_config, config)
            sequence_length = config.get('sequence_length', 60)

            logger.info(f"📊 准备训练数据: {days}天, 序列长度: {sequence_length}")

            # 生成缓存键
            cache_key = self._generate_cache_key(config)
            logger.info(f"🔑 数据缓存键: {cache_key}")

            # 更新进度和状态信息
            if task_id:
                self._update_task_status(task_id, 'running',
                    logs=json.dumps({
                        'stage': 'data_preparation',
                        'message': f'开始准备训练数据: {days}天, 序列长度: {sequence_length}',
                        'data_config': data_config,
                        'cache_key': cache_key
                    }))
                self._update_task_progress(task_id, 12, 0, 0, 0)

            # 尝试从缓存加载数据
            logger.info("🔍 检查数据缓存...")
            cached_data = self._load_data_cache(cache_key)

            if cached_data and self._validate_data_integrity(cached_data):
                logger.info("✅ 使用缓存数据，跳过数据获取和预处理")
                if task_id:
                    self._update_task_status(task_id, 'running',
                        logs=json.dumps({
                            'stage': 'cache_loaded',
                            'message': '成功从缓存加载数据',
                            'cache_key': cache_key,
                            'data_shape': {
                                'X_train': list(cached_data['X_train'].shape),
                                'X_val': list(cached_data['X_val'].shape),
                                'y_train': list(cached_data['y_train'].shape),
                                'y_val': list(cached_data['y_val'].shape)
                            }
                        }))
                    self._update_task_progress(task_id, 25, 0, 0, 0)

                return cached_data['X_train'], cached_data['X_val'], cached_data['y_train'], cached_data['y_val']

            # 从MT5获取真实历史数据
            logger.info(f"🔗 连接MT5获取真实历史数据...")
            if task_id:
                self._update_task_status(task_id, 'running',
                    logs=json.dumps({
                        'stage': 'data_fetching',
                        'message': '正在连接MT5获取历史数据...',
                        'symbol': config.get('symbol', 'XAUUSD'),
                        'timeframe': config.get('timeframe', 'H1'),
                        'days': days
                    }))
                self._update_task_progress(task_id, 15, 0, 0, 0)

            # 确保MT5连接正常
            from services.mt5_service import mt5_service
            if not mt5_service.is_connected():
                logger.info("🔄 MT5未连接，尝试重新连接...")
                if not mt5_service.connect():
                    raise Exception("无法连接MT5，请检查MT5终端是否运行并已登录账户")
                logger.info("✅ MT5重新连接成功")

            price_data = self._get_mt5_historical_data(config, data_config, days)

            if price_data is None or len(price_data) == 0:
                # 再次尝试连接并获取数据
                logger.warning("⚠️ 首次数据获取失败，尝试重新连接MT5...")
                mt5_service.disconnect()
                time.sleep(2)
                if mt5_service.connect():
                    logger.info("✅ MT5重新连接成功，再次尝试获取数据...")
                    price_data = self._get_mt5_historical_data(config, data_config, days)

                if price_data is None or len(price_data) == 0:
                    raise Exception("无法获取MT5历史数据，请检查MT5连接和数据权限")

            logger.info(f"✅ 成功获取MT5数据: {len(price_data)}条记录")

            # 更新数据获取完成状态
            if task_id:
                self._update_task_status(task_id, 'running',
                    logs=json.dumps({
                        'stage': 'data_fetched',
                        'message': f'成功获取{len(price_data)}条历史数据',
                        'data_points': len(price_data),
                        'data_range': {
                            'start': f'{price_data[0, 3]:.5f}' if len(price_data) > 0 else None,  # 使用收盘价作为范围指示
                            'end': f'{price_data[-1, 3]:.5f}' if len(price_data) > 0 else None
                        }
                    }))
                self._update_task_progress(task_id, 18, 0, 0, 0)

            # 添加技术指标
            logger.info("📈 计算技术指标...")
            if task_id:
                self._update_task_status(task_id, 'running',
                    logs=json.dumps({
                        'stage': 'feature_calculation',
                        'message': '正在计算技术指标...',
                        'features': config.get('features', ['close', 'volume'])
                    }))
                self._update_task_progress(task_id, 20, 0, 0, 0)

            features = self._calculate_features(price_data, config)

            # 验证特征数据
            logger.info("🔍 验证特征数据...")
            if features is None:
                raise Exception("特征计算返回None")

            if not isinstance(features, np.ndarray):
                raise Exception(f"特征数据类型错误: {type(features)}")

            if len(features.shape) != 2:
                raise Exception(f"特征数据形状错误: {features.shape}, 期望2维数组")

            if features.shape[0] == 0:
                raise Exception("特征数据为空")

            logger.info(f"✅ 特征验证通过: 形状{features.shape}, 类型{features.dtype}")

            # 创建序列数据
            logger.info("🔢 创建训练序列...")
            if task_id:
                self._update_task_status(task_id, 'running',
                    logs=json.dumps({
                        'stage': 'sequence_creation',
                        'message': f'正在创建训练序列，序列长度: {sequence_length}',
                        'feature_shape': list(features.shape),
                        'feature_count': features.shape[1],
                        'data_points': features.shape[0]
                    }))
                self._update_task_progress(task_id, 22, 0, 0, 0)

            X, y = self._create_sequences(features, sequence_length)

            # 分割训练和验证集
            logger.info("✂️ 分割训练集和验证集...")
            if task_id:
                self._update_task_status(task_id, 'running',
                    logs=json.dumps({
                        'stage': 'data_splitting',
                        'message': '正在分割训练集和验证集...',
                        'total_sequences': len(X),
                        'validation_split': 0.2
                    }))
                self._update_task_progress(task_id, 24, 0, 0, 0)

            split_idx = int(len(X) * 0.8)
            X_train, X_val = X[:split_idx], X[split_idx:]
            y_train, y_val = y[:split_idx], y[split_idx:]

            logger.info(f"📊 数据准备完成: 训练集 {X_train.shape}, 验证集 {X_val.shape}")

            # 数据验证检查
            if np.isnan(X_train).any() or np.isnan(X_val).any():
                raise ValueError("数据包含NaN值，请检查数据源")

            if np.isinf(X_train).any() or np.isinf(X_val).any():
                raise ValueError("数据包含无穷大值，请检查数据源")

            # 检查标签分布
            unique, counts = np.unique(y_train, return_counts=True)
            class_ratio = counts[1] / counts[0] if len(counts) > 1 else 1
            if class_ratio < 0.2 or class_ratio > 5:
                logger.warning(f"⚠️ 标签分布不平衡: 类别0={counts[0]}, 类别1={counts[1]}")

            # 保存数据到缓存
            logger.info("💾 保存数据到缓存...")
            cache_data = {
                'X_train': X_train,
                'X_val': X_val,
                'y_train': y_train,
                'y_val': y_val,
                'feature_names': getattr(self, '_last_feature_names', []),
                'data_info': {
                    'symbol': config.get('symbol'),
                    'timeframe': config.get('timeframe'),
                    'days': days,
                    'sequence_length': sequence_length,
                    'train_samples': len(X_train),
                    'val_samples': len(X_val),
                    'feature_count': X_train.shape[-1] if len(X_train.shape) > 1 else 1,
                    'class_distribution': dict(zip(unique.astype(int), counts)),
                    'created_at': datetime.now().isoformat()
                }
            }
            self._save_data_cache(cache_key, cache_data)

            # 🔧 修复：更新数据准备完成状态 - 只在_prepare_data_async中更新，避免重复更新
            # 注意：这里不更新状态，由_prepare_data_async统一处理状态更新
                
            return X_train, X_val, y_train, y_val
             
        except Exception as e:
            logger.error(f"❌ 数据准备失败: {e}")
            import traceback
            logger.error(f"详细错误堆栈: {traceback.format_exc()}")
            # 新增：失败回调
            if task_id:
                self._training_callback(task_id, "error", 0, 0, 0, str(e))
            return None, None, None, None

    
    def _check_and_reconnect_mt5(self, symbol: str = "XAUUSD", max_retries: int = 3) -> bool:
        """检测MT5连接状态，如果未连接则尝试重新连接"""
        try:
            import MetaTrader5 as mt5

            logger.info(f"🔍 检查MT5连接状态...")

            # 检查MT5是否已初始化
            if not mt5.initialize():
                logger.warning("⚠️ MT5未初始化，尝试初始化...")

                for retry in range(max_retries):
                    try:
                        logger.info(f"🔄 第{retry + 1}次尝试连接MT5...")

                        # 尝试初始化MT5
                        if mt5.initialize(timeout=10000):  # 10秒超时
                            logger.info("✅ MT5初始化成功")

                            # 验证连接是否真正可用
                            terminal_info = mt5.terminal_info()
                            if terminal_info is None:
                                logger.warning("⚠️ 无法获取终端信息，连接可能不稳定")
                                mt5.shutdown()
                                if retry < max_retries - 1:
                                    time.sleep(2)
                                    continue
                                else:
                                    return False

                            # 测试数据获取功能
                            logger.info(f"🧪 测试数据获取功能...")
                            test_rates = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_H1, 0, 1)
                            if test_rates is None or len(test_rates) == 0:
                                logger.warning(f"⚠️ 无法获取{symbol}测试数据")
                                error = mt5.last_error()
                                logger.warning(f"   MT5错误: {error}")

                                # 如果是符号问题，尝试其他符号
                                if error[0] == 4106:  # 未知符号
                                    logger.info(f"🔄 尝试使用其他符号测试...")
                                    for test_symbol in ["EURUSD", "GBPUSD", "USDJPY"]:
                                        test_rates = mt5.copy_rates_from_pos(test_symbol, mt5.TIMEFRAME_H1, 0, 1)
                                        if test_rates is not None and len(test_rates) > 0:
                                            logger.info(f"✅ 使用{test_symbol}测试成功，MT5连接正常")
                                            return True

                                if retry < max_retries - 1:
                                    mt5.shutdown()
                                    time.sleep(3)
                                    continue
                                else:
                                    mt5.shutdown()
                                    return False
                            else:
                                logger.info(f"✅ MT5连接测试成功，获取到{len(test_rates)}条{symbol}数据")
                                return True
                        else:
                            logger.warning(f"⚠️ 第{retry + 1}次MT5初始化失败")
                            error = mt5.last_error()
                            logger.warning(f"   MT5错误: {error}")

                            if retry < max_retries - 1:
                                time.sleep(2)
                            else:
                                logger.error("❌ 所有MT5连接尝试都失败了")
                                return False

                    except Exception as e:
                        logger.warning(f"⚠️ 第{retry + 1}次连接异常: {e}")
                        if retry < max_retries - 1:
                            time.sleep(2)
                        else:
                            return False

                return False
            else:
                # MT5已初始化，验证连接状态
                logger.info("📡 MT5已初始化，验证连接状态...")

                try:
                    # 检查终端信息
                    terminal_info = mt5.terminal_info()
                    if terminal_info is None:
                        logger.warning("⚠️ 无法获取终端信息，尝试重新连接...")
                        mt5.shutdown()
                        return self._check_and_reconnect_mt5(symbol, max_retries)

                    # 检查账户信息
                    account_info = mt5.account_info()
                    if account_info is None:
                        logger.warning("⚠️ 无法获取账户信息，尝试重新连接...")
                        mt5.shutdown()
                        return self._check_and_reconnect_mt5(symbol, max_retries)

                    # 测试数据获取
                    test_rates = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_H1, 0, 1)
                    if test_rates is None or len(test_rates) == 0:
                        logger.warning(f"⚠️ 无法获取{symbol}数据，尝试重新连接...")
                        mt5.shutdown()
                        return self._check_and_reconnect_mt5(symbol, max_retries)

                    logger.info(f"✅ MT5连接状态良好")
                    logger.info(f"   终端: {terminal_info.name}")
                    logger.info(f"   账户: {account_info.login}")
                    logger.info(f"   服务器: {account_info.server}")
                    return True

                except Exception as e:
                    logger.warning(f"⚠️ 连接验证异常: {e}")
                    mt5.shutdown()
                    return self._check_and_reconnect_mt5(symbol, max_retries)

        except ImportError:
            logger.error("❌ MetaTrader5模块未安装")
            return False
        except Exception as e:
            logger.error(f"❌ MT5连接检查失败: {e}")
            return False

    def _get_mt5_data_chunked(self, symbol: str, timeframe: str, start_date: datetime,
                             end_date: datetime, chunk_days: int = 30) -> np.ndarray:
        """分块获取MT5历史数据，避免大量数据请求失败"""
        try:
            import MetaTrader5 as mt5

            # 首先检查并确保MT5连接正常
            logger.info(f"🔍 开始分块获取数据前，检查MT5连接状态...")
            if not self._check_and_reconnect_mt5(symbol):
                logger.error("❌ MT5连接失败，无法获取数据")
                return None

            # 转换时间框架
            mt5_timeframe = self._convert_timeframe_to_mt5(timeframe)
            if mt5_timeframe is None:
                logger.error(f"❌ 不支持的时间框架: {timeframe}")
                return None
            
            logger.info(f"📊 分块获取数据: {symbol} {timeframe}")
            logger.info(f"   总范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
            logger.info(f"   分块大小: {chunk_days}天")
            
            all_rates = []
            current_start = start_date
            chunk_count = 0
            failed_chunks = 0
            
            while current_start < end_date:
                chunk_count += 1
                current_end = min(current_start + timedelta(days=chunk_days), end_date)
                
                logger.info(f"🔄 获取块 {chunk_count}: {current_start.strftime('%Y-%m-%d')} 到 {current_end.strftime('%Y-%m-%d')}")
                
                # 尝试获取当前块的数据
                max_retries = 3
                for retry in range(max_retries):
                    try:
                        rates = mt5.copy_rates_range(symbol, mt5_timeframe, current_start, current_end)
                        if rates is not None and len(rates) > 0:
                            # 确保正确处理MT5结构化数组
                            if hasattr(rates, 'dtype') and rates.dtype.names:
                                # MT5结构化数组，转换为列表保持结构
                                all_rates.extend(rates.tolist())
                            else:
                                # 普通数组
                                all_rates.extend(rates)
                            logger.info(f"✅ 块 {chunk_count} 成功: {len(rates)} 条数据")
                            break
                        else:
                            if retry < max_retries - 1:
                                logger.warning(f"⚠️ 块 {chunk_count} 重试 {retry + 1}/{max_retries}")
                                time.sleep(2)
                            else:
                                logger.warning(f"⚠️ 块 {chunk_count} 获取失败")
                                failed_chunks += 1
                    except Exception as e:
                        logger.warning(f"⚠️ 块 {chunk_count} 异常: {e}")
                        if retry < max_retries - 1:
                            time.sleep(2)
                        else:
                            failed_chunks += 1
                
                current_start = current_end
                time.sleep(0.1)  # 避免请求过快
            
            logger.info(f"📊 分块获取完成: 总块数={chunk_count}, 失败块数={failed_chunks}")

            if len(all_rates) == 0:
                logger.error("❌ 所有数据块都获取失败")
                return None

            # 转换为numpy数组并验证结构
            try:
                # 检查原始数据结构
                logger.info(f"📊 all_rates类型: {type(all_rates)}, 长度: {len(all_rates)}")
                if len(all_rates) > 0:
                    logger.info(f"📊 第一个元素类型: {type(all_rates[0])}")
                    logger.info(f"📊 第一个元素: {all_rates[0]}")

                # 尝试不同的数组创建方法
                if len(all_rates) > 0 and isinstance(all_rates[0], (tuple, list)):
                    # 如果是元组或列表，直接创建数组
                    rates_array = np.array(all_rates)
                    logger.info(f"📊 从列表/元组创建数组形状: {rates_array.shape}")
                elif len(all_rates) > 0 and hasattr(all_rates[0], 'dtype'):
                    # 如果是numpy记录，需要特殊处理
                    rates_array = np.array([list(item) for item in all_rates])
                    logger.info(f"📊 从numpy记录创建数组形状: {rates_array.shape}")
                else:
                    # 尝试直接创建
                    rates_array = np.array(all_rates)
                    logger.info(f"📊 直接创建数组形状: {rates_array.shape}")

                # 检查数据维度
                if rates_array.ndim == 1:
                    logger.error("❌ 数据是一维的，尝试重新构造")
                    # 尝试重新构造数据
                    if len(all_rates) > 0:
                        # 检查是否是MT5结构化数据
                        sample = all_rates[0]
                        if hasattr(sample, '__len__') and len(sample) >= 5:
                            # 重新构造为二维数组
                            rates_array = np.array([[item[i] for i in range(len(item))] for item in all_rates])
                            logger.info(f"📊 重构后数组形状: {rates_array.shape}")
                        else:
                            logger.error("❌ 无法重构数据")
                            return None

                if rates_array.ndim != 2:
                    logger.error(f"❌ 数据维度错误: {rates_array.ndim} != 2")
                    return None

                if rates_array.shape[1] < 5:
                    logger.error(f"❌ 数据列数不足: {rates_array.shape[1]} < 5")
                    return None

                # 去重和排序（按时间）
                unique_rates = np.unique(rates_array, axis=0)
                logger.info(f"📊 去重后数据形状: {unique_rates.shape}")

                # 安全的时间排序
                if unique_rates.shape[0] > 0 and unique_rates.shape[1] > 0:
                    # 按第一列（时间）排序
                    time_indices = unique_rates[:, 0].argsort()
                    sorted_rates = unique_rates[time_indices]
                else:
                    logger.error("❌ 去重后数据为空")
                    return None

                # 检查是否为结构化数组
                if sorted_rates.dtype.names:
                    # 结构化数组，使用字段名
                    price_data = np.column_stack([
                        sorted_rates['open'],
                        sorted_rates['high'],
                        sorted_rates['low'],
                        sorted_rates['close'],
                        sorted_rates['tick_volume']
                    ])
                else:
                    # 普通数组，使用索引
                    if sorted_rates.shape[1] >= 6:  # time, open, high, low, close, volume
                        price_data = sorted_rates[:, 1:6]  # 跳过时间列
                    else:
                        logger.error(f"❌ 数据列数不足: {sorted_rates.shape[1]}")
                        return None

            except Exception as array_error:
                logger.error(f"❌ 数组处理失败: {array_error}")
                logger.error(f"   all_rates类型: {type(all_rates)}")
                logger.error(f"   all_rates长度: {len(all_rates)}")
                if len(all_rates) > 0:
                    logger.error(f"   第一个元素: {all_rates[0]}")
                    logger.error(f"   第一个元素类型: {type(all_rates[0])}")
                return None
            
            logger.info(f"✅ 分块获取成功: {len(price_data)} 条有效数据")
            return price_data
            
        except Exception as e:
            logger.error(f"❌ 分块数据获取失败: {e}")
            return None

    def _get_mt5_historical_data(self, config: Dict[str, Any], data_config: Dict[str, Any], days: int) -> Optional[np.ndarray]:
        """从MT5获取真实历史数据"""
        try:
            # 导入MT5相关模块
            try:
                import MetaTrader5 as mt5
                from datetime import datetime, timedelta
            except ImportError:
                logger.error("❌ MetaTrader5模块未安装，请运行: pip install MetaTrader5")
                return None

            # 获取配置参数
            symbol = config.get('symbol', 'XAUUSD')
            timeframe = config.get('timeframe', '1h')

            # 首先检查并确保MT5连接正常
            logger.info(f"🔍 开始获取历史数据前，检查MT5连接状态...")
            if not self._check_and_reconnect_mt5(symbol):
                logger.error("❌ MT5连接失败，无法获取历史数据")
                return None

            # 转换时间框架
            mt5_timeframe = self._convert_timeframe_to_mt5(timeframe)
            if mt5_timeframe is None:
                logger.error(f"❌ 不支持的时间框架: {timeframe}")
                return None

            # 计算日期范围
            date_range = self._get_date_range_info(data_config)
            end_date = datetime.strptime(date_range['end_date'], '%Y-%m-%d')
            start_date = datetime.strptime(date_range['start_date'], '%Y-%m-%d')

            logger.info(f"📅 获取数据范围: {symbol} {timeframe} 从 {start_date} 到 {end_date}")

            # 初始化MT5连接
            if not mt5.initialize():
                logger.error("❌ MT5初始化失败")
                return None

            try:
                # 单次初始化带超时
                logger.info(f"🔗 连接MT5({symbol}) 超时:5秒")
                if not mt5.initialize(timeout=5000):
                    logger.error("❌ MT5初始化失败: 连接超时")
                    return None
                
                # 获取历史数据（优化超时和重试机制）
                start_time = time.time()
                timeout = 60  # 减少超时到60秒，避免长时间卡住
                attempt = 0
                max_attempts = 3  # 最多尝试3次

                # 计算数据范围，如果超过90天，使用分块获取
                total_days = (end_date - start_date).days
                if total_days > 90:
                    logger.info(f"📊 数据范围较大({total_days}天)，使用分块获取策略")
                    
                    # 关闭当前MT5连接，使用分块方法
                    mt5.shutdown()
                    
                    # 使用分块获取
                    price_data = self._get_mt5_data_chunked(symbol, timeframe, start_date, end_date)
                    if price_data is not None:
                        logger.info(f"✅ 分块获取成功: {len(price_data)}条记录")
                        return price_data
                    else:
                        logger.error("❌ 分块获取也失败，尝试传统方法")
                        # 重新初始化MT5
                        if not mt5.initialize(timeout=5000):
                            logger.error("❌ MT5重新初始化失败")
                            return None
                
                # 传统获取方法（用于小数据量）
                while time.time() - start_time < timeout and attempt < max_attempts:
                    attempt += 1
                    logger.info(f"🔄 第{attempt}次尝试获取MT5数据...")

                    try:
                        rates = mt5.copy_rates_range(symbol, mt5_timeframe, start_date, end_date)
                        if rates is not None and len(rates) > 0:
                            logger.info(f"✅ 第{attempt}次尝试获取数据成功，获得{len(rates)}条记录")
                            break
                        else:
                            logger.warning(f"⚠️ 第{attempt}次获取数据为空")
                            # 添加更详细的错误信息
                            error = mt5.last_error()
                            logger.warning(f"   MT5错误: {error}")
                    except Exception as e:
                        logger.warning(f"⚠️ 第{attempt}次获取数据异常: {e}")

                    if attempt < max_attempts:
                        logger.info(f"等待3秒后重试...")
                        time.sleep(3)  # 等待3秒重试
                else:
                    if attempt >= max_attempts:
                        logger.error(f"❌ 获取MT5数据失败，已尝试{max_attempts}次")
                    logger.error(f"   数据范围: {start_date} 到 {end_date}")
                    logger.error(f"   品种: {symbol}, 时间框架: {timeframe}")
                    logger.error(f"   MT5时间框架: {mt5_timeframe}")
                    
                    # 检查日期范围是否合理
                    if end_date > datetime.now():
                        logger.error(f"   ⚠️ 结束日期是未来时间: {end_date}")
                    
                    # 获取MT5最后错误
                    try:
                        last_error = mt5.last_error()
                        logger.error(f"   MT5错误: {last_error}")
                    except:
                        pass
                    else:
                        logger.error(f"❌ 获取MT5数据超时（{timeout}秒）")
                    return None

                # 转换为numpy数组 [open, high, low, close, volume] - 修复版本
                logger.info(f"📊 MT5数据类型: {type(rates)}")
                logger.info(f"📊 MT5数据形状: {rates.shape}")
                logger.info(f"📊 MT5数据字段: {rates.dtype.names if hasattr(rates, 'dtype') and rates.dtype.names else 'N/A'}")

                try:
                    # 方法1: 如果是结构化数组，直接提取字段
                    if hasattr(rates, 'dtype') and rates.dtype.names:
                        # 确定成交量字段名
                        volume_field = None
                        for field in ['tick_volume', 'real_volume', 'volume']:
                            if field in rates.dtype.names:
                                volume_field = field
                                break

                        if volume_field is None:
                            logger.warning("⚠️ 未找到成交量字段，使用默认值")
                            volume_data = np.ones(len(rates)) * 1000  # 默认成交量
                        else:
                            volume_data = rates[volume_field]
                            logger.info(f"✅ 使用成交量字段: {volume_field}")

                        # 提取OHLCV数据
                        price_data = np.column_stack([
                            rates['open'],
                            rates['high'],
                            rates['low'],
                            rates['close'],
                            volume_data
                        ])

                        logger.info(f"✅ 结构化数组转换成功: {price_data.shape}")

                    else:
                        # 方法2: 如果是普通数组，使用原来的方法
                        logger.info("📊 使用原始转换方法")
                        price_data = np.array([
                            [rate['open'], rate['high'], rate['low'], rate['close'], rate.get('tick_volume', 1000)]
                            for rate in rates
                        ])

                        logger.info(f"✅ 原始方法转换成功: {price_data.shape}")

                    # 验证数据
                    if price_data.shape[1] != 5:
                        raise ValueError(f"数据列数不正确: {price_data.shape[1]}, 期望5列")

                    if len(price_data) == 0:
                        raise ValueError("转换后数据为空")

                    # 检查数据有效性
                    if np.any(np.isnan(price_data)) or np.any(np.isinf(price_data)):
                        logger.warning("⚠️ 数据包含NaN或Inf值，进行清理")
                        price_data = np.nan_to_num(price_data, nan=0.0, posinf=0.0, neginf=0.0)

                    logger.info(f"✅ 成功获取MT5数据: {len(price_data)}条 {symbol} {timeframe} 记录")
                    logger.info(f"📊 数据范围: {price_data[:, 3].min():.5f} - {price_data[:, 3].max():.5f}")
                    logger.info(f"📊 数据形状: {price_data.shape}, 数据类型: {price_data.dtype}")

                    return price_data

                except Exception as conversion_error:
                    logger.error(f"❌ 数据转换失败: {conversion_error}")
                    return None

            except Exception as e:
                logger.error(f"❌ 获取MT5数据异常: {str(e)}")
                return None
                
            finally:
                try:
                    logger.info("🔌 关闭MT5连接")
                    mt5.shutdown()
                except Exception as e:
                    logger.warning(f"⚠️ 关闭MT5连接失败: {str(e)}")

        except Exception as e:
            logger.error(f"❌ 获取MT5数据失败: {e}")
            return None

    def _convert_timeframe_to_mt5(self, timeframe: str):
        """转换时间框架到MT5格式（支持多种格式）"""
        try:
            import MetaTrader5 as mt5

            # 支持多种时间框架格式
            timeframe_map = {
                # 分钟级别
                '1m': mt5.TIMEFRAME_M1,
                'M1': mt5.TIMEFRAME_M1,
                '5m': mt5.TIMEFRAME_M5,
                'M5': mt5.TIMEFRAME_M5,
                '15m': mt5.TIMEFRAME_M15,
                'M15': mt5.TIMEFRAME_M15,
                '30m': mt5.TIMEFRAME_M30,
                'M30': mt5.TIMEFRAME_M30,

                # 小时级别
                '1h': mt5.TIMEFRAME_H1,
                'H1': mt5.TIMEFRAME_H1,
                '4h': mt5.TIMEFRAME_H4,
                'H4': mt5.TIMEFRAME_H4,

                # 日级别
                '1d': mt5.TIMEFRAME_D1,
                'D1': mt5.TIMEFRAME_D1,
                'daily': mt5.TIMEFRAME_D1,

                # 周级别
                '1w': mt5.TIMEFRAME_W1,
                'W1': mt5.TIMEFRAME_W1,
                'weekly': mt5.TIMEFRAME_W1,

                # 月级别
                '1M': mt5.TIMEFRAME_MN1,
                'MN1': mt5.TIMEFRAME_MN1,
                'monthly': mt5.TIMEFRAME_MN1
            }

            result = timeframe_map.get(timeframe)

            if result is None:
                logger.error(f"❌ 不支持的时间框架: {timeframe}")
                logger.info(f"💡 支持的时间框架: {list(timeframe_map.keys())}")
            else:
                logger.info(f"✅ 时间框架转换: {timeframe} -> {result}")

            return result

        except ImportError:
            logger.error("❌ MetaTrader5模块未安装")
            return None
    
    def _calculate_features(self, price_data: np.ndarray, config: Dict[str, Any]) -> np.ndarray:
        """计算特征（支持列表和字典两种配置格式）"""
        try:
            logger.info(f"📊 开始计算特征，数据形状: {price_data.shape}")

            # 获取特征配置
            features_config = config.get('features', ['close', 'volume'])

            # 支持两种配置格式
            if isinstance(features_config, list):
                # 新格式：['close', 'volume', 'high', 'low']
                return self._calculate_features_from_list(price_data, features_config)
            elif isinstance(features_config, dict):
                # 旧格式：{'price': True, 'technical': True}
                return self._calculate_features_from_dict(price_data, features_config)
            else:
                logger.warning(f"⚠️ 不支持的特征配置格式: {type(features_config)}")
                # 默认使用收盘价
                return price_data[:, 3:4]  # 只返回收盘价列

        except Exception as e:
            logger.error(f"❌ 特征计算失败: {e}")
            # 返回收盘价作为默认特征
            return price_data[:, 3:4]

    def _calculate_features_from_list(self, price_data: np.ndarray, feature_list: list) -> np.ndarray:
        """从特征列表计算特征（增强版本）"""
        try:
            # 详细的输入数据检查
            logger.info(f"📊 输入数据检查:")
            logger.info(f"   数据类型: {type(price_data)}")
            logger.info(f"   数据形状: {price_data.shape}")
            logger.info(f"   数据dtype: {price_data.dtype}")
            logger.info(f"   特征列表: {feature_list}")

            # 验证输入数据
            if not isinstance(price_data, np.ndarray):
                logger.error(f"❌ 输入数据不是NumPy数组: {type(price_data)}")
                return np.array([[0.0]] * len(price_data))  # 返回默认数据

            if len(price_data.shape) != 2:
                logger.error(f"❌ 数据形状不正确: {price_data.shape}, 期望2维数组")
                return np.array([[0.0]] * len(price_data))

            if price_data.shape[0] == 0:
                logger.error(f"❌ 数据为空")
                return np.array([[0.0]])

            logger.info(f"✅ 输入数据验证通过")

            features = []

            # 价格数据映射 [open, high, low, close, volume]
            price_map = {
                'open': 0,
                'high': 1,
                'low': 2,
                'close': 3,
                'volume': 4
            }

            logger.info(f"📈 开始计算特征: {feature_list}")

            # 提取指定的价格特征
            for i, feature_name in enumerate(feature_list):
                logger.info(f"🔄 处理特征 {i+1}/{len(feature_list)}: {feature_name}")

                if feature_name in price_map:
                    col_idx = price_map[feature_name]
                    logger.info(f"   映射到列索引: {col_idx}")

                    if col_idx < price_data.shape[1]:
                        try:
                            # 安全地提取特征数据
                            feature_data = price_data[:, col_idx]
                            logger.info(f"   提取数据形状: {feature_data.shape}")
                            logger.info(f"   数据范围: {feature_data.min():.5f} - {feature_data.max():.5f}")

                            # 检查数据有效性
                            if np.any(np.isnan(feature_data)) or np.any(np.isinf(feature_data)):
                                logger.warning(f"   ⚠️ 数据包含NaN或Inf，进行清理")
                                feature_data = np.nan_to_num(feature_data, nan=0.0, posinf=0.0, neginf=0.0)

                            # 标准化特征
                            feature_mean = feature_data.mean()
                            feature_std = feature_data.std()

                            logger.info(f"   统计信息: 均值={feature_mean:.5f}, 标准差={feature_std:.5f}")

                            if feature_std > 1e-10:
                                normalized_feature = (feature_data - feature_mean) / feature_std
                            else:
                                logger.warning(f"   ⚠️ 标准差过小，只进行中心化")
                                normalized_feature = feature_data - feature_mean

                            # 重塑为列向量
                            normalized_feature = normalized_feature.reshape(-1, 1)
                            features.append(normalized_feature)

                            logger.info(f"   ✅ 特征 {feature_name} 处理完成: 形状 {normalized_feature.shape}")

                        except Exception as feature_error:
                            logger.error(f"   ❌ 处理特征 {feature_name} 失败: {feature_error}")
                            # 添加默认特征
                            default_feature = np.zeros((price_data.shape[0], 1))
                            features.append(default_feature)
                            logger.info(f"   🔄 使用默认特征替代")

                    else:
                        logger.warning(f"   ⚠️ 特征 {feature_name} 超出数据列范围: {col_idx} >= {price_data.shape[1]}")
                        # 添加默认特征
                        default_feature = np.zeros((price_data.shape[0], 1))
                        features.append(default_feature)

                else:
                    logger.warning(f"   ⚠️ 不支持的特征: {feature_name}")
                    # 添加默认特征
                    default_feature = np.zeros((price_data.shape[0], 1))
                    features.append(default_feature)

            # 合并所有特征
            if features:
                try:
                    logger.info(f"🔗 合并 {len(features)} 个特征")
                    for i, feature in enumerate(features):
                        logger.info(f"   特征 {i}: 形状 {feature.shape}")

                    result = np.concatenate(features, axis=1)
                    logger.info(f"✅ 特征计算完成，最终形状: {result.shape}")
                    logger.info(f"📊 最终数据范围: {result.min():.5f} - {result.max():.5f}")

                    return result

                except Exception as concat_error:
                    logger.error(f"❌ 特征合并失败: {concat_error}")
                    # 返回第一个特征作为备选
                    if len(features) > 0:
                        logger.info(f"🔄 使用第一个特征作为备选")
                        return features[0]
                    else:
                        logger.info(f"🔄 使用收盘价作为备选")
                        return price_data[:, 3:4]
            else:
                logger.warning("⚠️ 没有有效特征，使用收盘价")
                try:
                    return price_data[:, 3:4]
                except Exception as fallback_error:
                    logger.error(f"❌ 连收盘价都无法提取: {fallback_error}")
                    # 最后的备选方案：返回全零数组
                    return np.zeros((price_data.shape[0], 1))

        except Exception as e:
            logger.error(f"❌ 列表特征计算失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")

            # 安全的备选方案
            try:
                if hasattr(price_data, 'shape') and len(price_data.shape) == 2 and price_data.shape[1] > 3:
                    return price_data[:, 3:4]  # 收盘价
                else:
                    return np.zeros((len(price_data) if hasattr(price_data, '__len__') else 100, 1))
            except:
                return np.zeros((100, 1))  # 最终备选

    def _calculate_features_from_dict(self, price_data: np.ndarray, features_config: dict) -> np.ndarray:
        """从特征字典计算特征（旧格式兼容）"""
        try:
            features = []

            # 价格特征
            if features_config.get('price', True):
                # 归一化价格数据
                normalized_prices = (price_data - price_data.mean(axis=0)) / (price_data.std(axis=0) + 1e-10)
                features.append(normalized_prices)

            # 技术指标
            if features_config.get('technical', True):
                # 简单移动平均
                sma_5 = np.convolve(price_data[:, 3], np.ones(5)/5, mode='same')
                sma_20 = np.convolve(price_data[:, 3], np.ones(20)/20, mode='same')

                # RSI (简化版)
                price_changes = np.diff(price_data[:, 3])
                gains = np.where(price_changes > 0, price_changes, 0)
                losses = np.where(price_changes < 0, -price_changes, 0)

                avg_gains = np.convolve(gains, np.ones(14)/14, mode='same')
                avg_losses = np.convolve(losses, np.ones(14)/14, mode='same')

                rs = avg_gains / (avg_losses + 1e-10)
                rsi = 100 - (100 / (1 + rs))
                rsi = np.concatenate([[50], rsi])  # 添加第一个值

                technical_features = np.column_stack([
                    sma_5, sma_20, rsi
                ])

                # 归一化技术指标
                technical_features = (technical_features - technical_features.mean(axis=0)) / (technical_features.std(axis=0) + 1e-10)
                features.append(technical_features)

            # 合并所有特征
            if features:
                return np.concatenate(features, axis=1)
            else:
                return price_data

        except Exception as e:
            logger.error(f"❌ 字典特征计算失败: {e}")
            return price_data[:, 3:4]
    
    def _create_sequences(self, data: np.ndarray, sequence_length: int) -> Tuple[np.ndarray, np.ndarray]:
        """创建序列数据（修复版本）"""
        try:
            logger.info(f"🔢 创建序列数据:")
            logger.info(f"   输入数据形状: {data.shape}")
            logger.info(f"   序列长度: {sequence_length}")

            # 验证输入数据
            if not isinstance(data, np.ndarray):
                raise ValueError(f"输入数据不是NumPy数组: {type(data)}")

            if len(data.shape) != 2:
                raise ValueError(f"输入数据形状不正确: {data.shape}, 期望2维数组")

            if len(data) <= sequence_length:
                raise ValueError(f"数据长度({len(data)})不足以创建序列(需要>{sequence_length})")

            X, y = [], []

            # 确定用于预测的列（通常是第一列，代表主要特征如收盘价）
            target_col = 0  # 使用第一列作为预测目标

            logger.info(f"   使用第{target_col}列作为预测目标")

            for i in range(sequence_length, len(data)):
                try:
                    # 创建输入序列
                    sequence = data[i-sequence_length:i]
                    X.append(sequence)

                    # 创建预测目标（价格变化方向）
                    current_value = data[i-1, target_col]
                    next_value = data[i, target_col]

                    # 二分类：上涨(1)或下跌(0)
                    y.append(1 if next_value > current_value else 0)

                except IndexError as idx_error:
                    logger.error(f"❌ 索引错误在位置{i}: {idx_error}")
                    logger.error(f"   数据形状: {data.shape}, 尝试访问: [{i-sequence_length}:{i}] 和 [{i}, {target_col}]")
                    raise

            X_array = np.array(X)
            y_array = np.array(y)

            logger.info(f"✅ 序列创建完成:")
            logger.info(f"   X形状: {X_array.shape}")
            logger.info(f"   y形状: {y_array.shape}")
            logger.info(f"   序列数量: {len(X_array)}")

            return X_array, y_array

        except Exception as e:
            logger.error(f"❌ 序列创建失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")

            # 返回最小可用的序列
            try:
                min_sequences = max(1, min(10, len(data) - sequence_length))
                X_fallback = np.zeros((min_sequences, sequence_length, data.shape[1]))
                y_fallback = np.zeros(min_sequences)
                logger.info(f"🔄 返回备用序列: X{X_fallback.shape}, y{y_fallback.shape}")
                return X_fallback, y_fallback
            except:
                # 最终备选方案
                X_final = np.zeros((1, sequence_length, 1))
                y_final = np.zeros(1)
                logger.info(f"🔄 返回最终备用序列: X{X_final.shape}, y{y_final.shape}")
                return X_final, y_final
    
    def _create_model(self, config: Dict[str, Any], input_size: int) -> nn.Module:
        """创建模型"""
        model_type = config.get('model_type', 'lstm').lower()
        hidden_size = config.get('hidden_size', 128)
        num_layers = config.get('num_layers', 2)
        dropout = config.get('dropout', 0.2)
        output_size = 1  # 二分类

        logger.info(f"🧠 创建 {model_type.upper()} 模型 (输入维度: {input_size}, 隐藏层: {hidden_size})")

        if model_type == 'lstm':
            model = LSTMModel(
                input_size=input_size,
                hidden_size=hidden_size,
                num_layers=num_layers,
                output_size=output_size,
                dropout=dropout
            )
        elif model_type == 'gru':
            model = GRUModel(
                input_size=input_size,
                hidden_size=hidden_size,
                num_layers=num_layers,
                output_size=output_size,
                dropout=dropout
            )
        elif model_type == 'transformer':
            # Transformer需要确保hidden_size能被nhead整除
            nhead = 8
            if hidden_size % nhead != 0:
                hidden_size = ((hidden_size // nhead) + 1) * nhead
                logger.info(f"🔧 调整Transformer隐藏层大小为 {hidden_size} (确保能被注意力头数整除)")

            model = TransformerModel(
                input_size=input_size,
                hidden_size=hidden_size,
                num_layers=num_layers,
                output_size=output_size,
                dropout=dropout,
                nhead=nhead
            )
        elif model_type == 'cnn_lstm':
            model = CNNLSTMModel(
                input_size=input_size,
                hidden_size=hidden_size,
                num_layers=num_layers,
                output_size=output_size,
                dropout=dropout
            )
        elif model_type == 'attention_lstm':
            model = AttentionLSTMModel(
                input_size=input_size,
                hidden_size=hidden_size,
                num_layers=num_layers,
                output_size=output_size,
                dropout=dropout
            )
        else:
            supported_types = ['lstm', 'gru', 'transformer', 'cnn_lstm', 'attention_lstm']
            raise ValueError(f"不支持的模型类型: {model_type}。支持的类型: {supported_types}")

        # 移动到设备
        model = model.to(self.device)

        # 打印模型信息
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

        logger.info(f"📊 模型参数统计:")
        logger.info(f"   总参数: {total_params:,}")
        logger.info(f"   可训练参数: {trainable_params:,}")
        logger.info(f"   模型大小: {total_params * 4 / 1024 / 1024:.2f} MB")

        return model
    
    def _train_model(self, model: nn.Module, X_train: np.ndarray, X_val: np.ndarray,
                    y_train: np.ndarray, y_val: np.ndarray, config: Dict[str, Any],
                    task_id: str) -> Dict[str, Any]:
        """训练模型"""

        # 创建数据加载器
        logger.info("📦 创建数据加载器...")
        train_dataset = TradingDataset(X_train, y_train)
        val_dataset = TradingDataset(X_val, y_val)

        # 优化数据加载器设置以防止卡住
        batch_size = min(config.get('batch_size', 32), 16)  # 限制最大批次大小

        # Windows系统上num_workers=0时不能使用timeout参数
        train_loader = DataLoader(
            train_dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=0,  # Windows上必须设为0
            pin_memory=True if self.device.type == 'cuda' else False
            # 注意：num_workers=0时不能使用timeout和persistent_workers参数
        )
        val_loader = DataLoader(
            val_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=0,
            pin_memory=True if self.device.type == 'cuda' else False
        )

        # 定义损失函数和优化器 - 改进版
        logger.info("⚙️ 初始化优化器...")

        # 使用标签平滑的损失函数，减少过拟合
        criterion = nn.BCEWithLogitsLoss(pos_weight=torch.tensor(1.0))

        # 获取训练配置参数（需要在使用前定义）
        epochs = config.get('epochs', 100)
        learning_rate = config.get('learning_rate', 0.001)

        # 改进的优化器配置
        optimizer = optim.AdamW(  # 使用AdamW替代Adam，更好的权重衰减
            model.parameters(),
            lr=learning_rate,
            weight_decay=config.get('weight_decay', 0.01),  # 增加权重衰减
            betas=(0.9, 0.999),
            eps=1e-8
        )

        # 改进的学习率调度器 - 使用余弦退火
        total_steps = len(train_loader) * epochs
        warmup_steps = int(0.1 * total_steps)  # 10%的步数用于预热

        # 组合调度器：预热 + 余弦退火
        def lr_lambda(step):
            if step < warmup_steps:
                # 预热阶段：线性增长
                return step / warmup_steps
            else:
                # 余弦退火阶段
                progress = (step - warmup_steps) / (total_steps - warmup_steps)
                return 0.5 * (1 + np.cos(np.pi * progress))

        scheduler = optim.lr_scheduler.LambdaLR(optimizer, lr_lambda)

        # 备用调度器：验证损失平台期调度
        plateau_scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode='min',
            factor=0.5,
            patience=8,
            verbose=True,
            min_lr=1e-7
        )

        # 训练历史
        history = {
            'train_loss': [],
            'val_loss': [],
            'train_acc': [],
            'val_acc': []
        }

        # 检查点配置
        checkpoint_dir = os.path.join(self.models_path, 'checkpoints', task_id)
        if not os.path.exists(checkpoint_dir):
            os.makedirs(checkpoint_dir)

        best_model_path = os.path.join(checkpoint_dir, 'best_model.pt')
        latest_checkpoint_path = os.path.join(checkpoint_dir, 'latest_checkpoint.pt')

        best_val_loss = float('inf')
        patience_counter = 0
        start_epoch = 0

        # 调整早停参数：更宽松的设置
        patience = config.get('patience', 20)  # 从10增加到20

        # 早停开关：允许用户禁用早停
        early_stopping_enabled = config.get('early_stopping', True)
        min_epochs = config.get('min_epochs', 20)  # 最少训练轮次

        # 检查点保存间隔
        checkpoint_interval = config.get('checkpoint_interval', 10)  # 每10个epoch保存一次

        # 尝试恢复检查点
        if os.path.exists(latest_checkpoint_path):
            try:
                logger.info("🔄 发现检查点，尝试恢复训练...")
                checkpoint = torch.load(latest_checkpoint_path, map_location=self.device)
                model.load_state_dict(checkpoint['model_state_dict'])
                optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
                start_epoch = checkpoint['epoch'] + 1
                best_val_loss = checkpoint['best_val_loss']
                history = checkpoint['history']
                patience_counter = checkpoint.get('patience_counter', 0)
                logger.info(f"✅ 从第{start_epoch}轮恢复训练")
            except Exception as e:
                logger.warning(f"⚠️ 检查点恢复失败，从头开始训练: {e}")
                start_epoch = 0

        logger.info(f"📊 训练配置:")
        logger.info(f"   总轮次: {epochs}")
        logger.info(f"   开始轮次: {start_epoch}")
        logger.info(f"   早停启用: {early_stopping_enabled}")
        logger.info(f"   检查点间隔: {checkpoint_interval}")
        if early_stopping_enabled:
            logger.info(f"   早停耐心值: {patience}")
            logger.info(f"   最少训练轮次: {min_epochs}")

        # 初始化训练进度（30%开始，70%结束）
        base_progress = 30
        training_progress_range = 70

        logger.info(f"🏋️ 开始训练 {epochs} 轮...")
        print(f"🚀 开始AI模型训练: {epochs} 轮次")
        print(f"📊 训练样本: {len(X_train)}, 验证样本: {len(X_val)}")
        print(f"🔧 批次大小: {config.get('batch_size', 32)}, 学习率: {config.get('learning_rate', 0.001)}")

        # 更新状态：开始模型训练
        self._update_task_status(task_id, 'running',
            logs=json.dumps({
                'stage': 'model_training',
                'message': f'开始训练模型，共{epochs}轮，从第{start_epoch}轮开始',
                'epochs': epochs,
                'start_epoch': start_epoch,
                'checkpoint_enabled': True,
                'batch_size': config.get('batch_size', 32),
                'learning_rate': config.get('learning_rate', 0.001),
                'train_samples': len(X_train),
                'val_samples': len(X_val)
            }))
        self._update_task_progress(task_id, base_progress, 0, 0.0, 0.0)

        # 添加训练开始的强制进度更新
        logger.info("🔄 强制更新训练开始进度...")
        self._update_task_progress(task_id, base_progress + 1, 0, 0.0, 0.0)  # 31%

        # 测试数据加载器是否正常工作
        logger.info("🧪 测试数据加载器...")
        try:
            first_batch = next(iter(train_loader))
            logger.info(f"✅ 数据加载器测试成功，批次形状: {first_batch[0].shape}")
            self._update_task_progress(task_id, base_progress + 2, 0, 0.0, 0.0)  # 32%
        except Exception as e:
            logger.error(f"❌ 数据加载器测试失败: {e}")
            raise Exception(f"数据加载器错误: {e}")

        # 添加心跳检测
        last_heartbeat = time.time()
        heartbeat_interval = 30  # 30秒心跳间隔

        # 训练循环异常重试机制
        max_retries = 3
        retry_count = 0

        for epoch in range(start_epoch, epochs):
            try:
                self._check_training_timeout()  # 检查训练超时

                logger.info(f"🔄 开始第 {epoch + 1}/{epochs} 轮训练...")

                # 强制更新进度，确保不卡在25%
                epoch_progress = base_progress + 3 + (epoch * (training_progress_range - 3) // epochs)
                self._update_task_progress(task_id, epoch_progress, epoch + 1, 0.0, 0.0)  # 修复：epoch应该是epoch+1

                # 添加调试日志
                logger.info(f"🔄 Epoch {epoch + 1}/{epochs} 开始，进度: {epoch_progress:.1f}%")

                # 更新心跳
                current_time = time.time()
                if current_time - last_heartbeat > heartbeat_interval:
                    logger.info(f"💓 训练心跳: Epoch {epoch + 1}/{epochs}")
                    last_heartbeat = current_time

                    # 清理GPU内存碎片
                    if self.device.type == 'cuda':
                        torch.cuda.empty_cache()

                # 检查训练控制状态
                control_state = self.training_control.get(task_id, {})

                # 检查停止信号
                if control_state.get('stop', False):
                    logger.info(f"🛑 训练被用户停止 (Epoch {epoch + 1})")
                    break

                # 检查暂停信号
                while control_state.get('pause', False):
                    logger.info(f"⏸️ 训练已暂停 (Epoch {epoch + 1})")
                    time.sleep(1)  # 等待1秒后重新检查
                    control_state = self.training_control.get(task_id, {})

                    # 在暂停期间也检查停止信号
                    if control_state.get('stop', False):
                        logger.info(f"🛑 训练在暂停期间被停止")
                        break

                # 如果在暂停检查中收到停止信号，跳出主循环
                if control_state.get('stop', False):
                    break

                # 训练阶段（带超时检查）
                epoch_start_time = time.time()
                model.train()
                train_loss = 0
                train_correct = 0
                train_total = 0

                # 设置每个epoch的最大执行时间（3分钟，更严格的超时）
                max_epoch_time = 3 * 60

                # 训练批次 - 改进版
                logger.info(f"📦 开始处理训练批次，总批次数: {len(train_loader)}")
                batch_count = 0

                # 添加训练循环开始的明确标记
                logger.info(f"🚀 Epoch {epoch + 1} 训练循环开始")

                # 添加批次循环超时检查
                batch_loop_start = time.time()
                max_batch_loop_time = 120  # 2分钟批次循环超时

                for batch_idx, (batch_X, batch_y) in enumerate(train_loader):
                    batch_count += 1

                    # 每5个批次更新一次进度和日志
                    if batch_idx % 5 == 0:
                        logger.info(f"📦 处理批次 {batch_idx + 1}/{len(train_loader)}, 形状: {batch_X.shape}")

                        # 检查批次循环是否超时
                        if time.time() - batch_loop_start > max_batch_loop_time:
                            logger.error(f"❌ 批次循环超时，已处理 {batch_idx + 1} 个批次")
                            raise TimeoutError(f"批次循环超时（超过{max_batch_loop_time}秒）")

                    # 检查是否超时
                    if time.time() - epoch_start_time > max_epoch_time:
                        raise TimeoutError(f"训练epoch {epoch+1} 超时（超过{max_epoch_time}秒）")

                    batch_X = batch_X.to(self.device)
                    batch_y = batch_y.to(self.device).float()

                    # 混合精度训练（如果支持）
                    if hasattr(torch.cuda, 'amp') and self.device.type == 'cuda':
                        with torch.cuda.amp.autocast():
                            outputs = model(batch_X).squeeze()
                            loss = criterion(outputs, batch_y)

                        # 梯度缩放
                        if not hasattr(self, 'scaler'):
                            self.scaler = torch.cuda.amp.GradScaler()

                        optimizer.zero_grad()
                        self.scaler.scale(loss).backward()

                        # 梯度裁剪
                        self.scaler.unscale_(optimizer)
                        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

                        self.scaler.step(optimizer)
                        self.scaler.update()
                    else:
                        # 标准训练
                        optimizer.zero_grad()
                        outputs = model(batch_X).squeeze()
                        loss = criterion(outputs, batch_y)
                        loss.backward()

                        # 梯度裁剪
                        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                        optimizer.step()

                    # 更新学习率调度器（每步）
                    scheduler.step()

                    train_loss += loss.item()
                    predicted = (torch.sigmoid(outputs) > 0.5).float()
                    train_total += batch_y.size(0)
                    train_correct += (predicted == batch_y).sum().item()
                    
                    # 每5个批次记录一次并更新数据库
                    if batch_idx % 5 == 0:
                        logger.debug(f"Epoch {epoch+1} 批次 {batch_idx}/{len(train_loader)}: 当前损失 {loss.item():.4f}")

                        # 更频繁的数据库更新以防止卡住检测
                        if batch_idx % 10 == 0:
                            batch_progress = (epoch + batch_idx / len(train_loader)) / epochs
                            current_progress = base_progress + (batch_progress * training_progress_range)
                            current_progress = min(95, current_progress)

                            # 强制输出训练进度
                            print(f"🔄 训练中: Epoch {epoch + 1}/{epochs}, Batch {batch_idx}/{len(train_loader)}, 进度: {current_progress:.1f}%, 损失: {loss.item():.4f}")

                            self._update_task_progress(
                                task_id, current_progress, epoch + 1, loss.item(), 0
                            )

                # 验证阶段（带超时检查）- 改进版
                val_start_time = time.time()
                model.eval()
                val_loss = 0
                val_correct = 0
                val_total = 0

                # 用于计算更多评估指标
                all_predictions = []
                all_targets = []

                with torch.no_grad():
                    for batch_idx, (batch_X, batch_y) in enumerate(val_loader):
                        # 检查是否超时
                        if time.time() - val_start_time > max_epoch_time:
                            raise TimeoutError(f"验证epoch {epoch+1} 超时（超过{max_epoch_time}秒）")

                        batch_X = batch_X.to(self.device)
                        batch_y = batch_y.to(self.device).float()

                        # 混合精度推理
                        if hasattr(torch.cuda, 'amp') and self.device.type == 'cuda':
                            with torch.cuda.amp.autocast():
                                outputs = model(batch_X).squeeze()
                                loss = criterion(outputs, batch_y)
                        else:
                            outputs = model(batch_X).squeeze()
                            loss = criterion(outputs, batch_y)

                        val_loss += loss.item()
                        probabilities = torch.sigmoid(outputs)
                        predicted = (probabilities > 0.5).float()
                        val_total += batch_y.size(0)
                        val_correct += (predicted == batch_y).sum().item()

                        # 收集预测和目标用于详细评估
                        all_predictions.extend(probabilities.cpu().numpy())
                        all_targets.extend(batch_y.cpu().numpy())

                        # 每10个批次记录一次
                        if batch_idx % 10 == 0:
                            logger.debug(f"验证 Epoch {epoch+1} 批次 {batch_idx}/{len(val_loader)}: 当前损失 {loss.item():.4f}")

                # 计算详细的验证指标
                all_predictions = np.array(all_predictions)
                all_targets = np.array(all_targets)

                # 计算AUC、精确率、召回率等
                try:
                    from sklearn.metrics import roc_auc_score, precision_score, recall_score, f1_score

                    val_auc = roc_auc_score(all_targets, all_predictions) if len(np.unique(all_targets)) > 1 else 0.5
                    val_precision = precision_score(all_targets, (all_predictions > 0.5).astype(int), zero_division=0)
                    val_recall = recall_score(all_targets, (all_predictions > 0.5).astype(int), zero_division=0)
                    val_f1 = f1_score(all_targets, (all_predictions > 0.5).astype(int), zero_division=0)
                except ImportError:
                    # 如果sklearn不可用，使用简单计算
                    val_auc = 0.5
                    val_precision = val_correct / max(val_total, 1)
                    val_recall = val_correct / max(val_total, 1)
                    val_f1 = 2 * val_precision * val_recall / max(val_precision + val_recall, 1e-8)

                # 计算平均损失和准确率
                avg_train_loss = train_loss / len(train_loader)
                avg_val_loss = val_loss / len(val_loader)
                train_acc = train_correct / train_total
                val_acc = val_correct / val_total

                # 记录历史
                history['train_loss'].append(avg_train_loss)
                history['val_loss'].append(avg_val_loss)
                history['train_acc'].append(train_acc)
                history['val_acc'].append(val_acc)

                # 更新任务进度（30%-95%范围内）
                epoch_progress = (epoch + 1) / epochs
                progress = base_progress + (epoch_progress * training_progress_range)
                progress = min(95, progress)  # 最大95%，留5%给最终保存

                self._update_task_progress(
                    task_id, progress, epoch + 1, avg_train_loss, avg_val_loss
                )

                # 更新详细的训练日志
                self._update_task_status(task_id, 'running',
                    logs=json.dumps({
                        'stage': 'model_training',
                        'message': f'训练进行中 - Epoch {epoch + 1}/{epochs}',
                        'epoch': epoch + 1,
                        'total_epochs': epochs,
                        'train_loss': avg_train_loss,
                        'val_loss': avg_val_loss,
                        'train_acc': train_acc,
                        'val_acc': val_acc,
                        'best_val_loss': best_val_loss,
                        'patience_counter': patience_counter if early_stopping_enabled else 0,
                        'progress_percent': progress
                    }))
            
                # 早停检查（改进版本）
                if early_stopping_enabled and epoch + 1 >= min_epochs:
                    if avg_val_loss < best_val_loss:
                        best_val_loss = avg_val_loss
                        patience_counter = 0
                        logger.debug(f"验证损失改善: {avg_val_loss:.4f} (最佳: {best_val_loss:.4f})")
                    else:
                        patience_counter += 1
                        logger.debug(f"验证损失未改善，耐心计数: {patience_counter}/{patience}")

                        if patience_counter >= patience:
                            logger.info(f"🛑 早停触发，在第 {epoch + 1} 轮停止训练")
                            logger.info(f"   原因: 验证损失连续 {patience} 轮未改善")
                            logger.info(f"   最佳验证损失: {best_val_loss:.4f}")
                            logger.info(f"   当前验证损失: {avg_val_loss:.4f}")
                            break
                elif avg_val_loss < best_val_loss:
                    # 即使早停未启用，也要跟踪最佳损失
                    best_val_loss = avg_val_loss

                # 保存最佳模型
                if avg_val_loss < best_val_loss:
                    try:
                        torch.save(model.state_dict(), best_model_path)
                        logger.debug(f"💾 保存最佳模型: {best_model_path}")
                    except Exception as e:
                        logger.warning(f"⚠️ 保存最佳模型失败: {e}")

                # 定期保存检查点
                if (epoch + 1) % checkpoint_interval == 0 or epoch == epochs - 1:
                    try:
                        checkpoint = {
                            'epoch': epoch,
                            'model_state_dict': model.state_dict(),
                            'optimizer_state_dict': optimizer.state_dict(),
                            'best_val_loss': best_val_loss,
                            'history': history,
                            'patience_counter': patience_counter,
                            'config': config
                        }
                        torch.save(checkpoint, latest_checkpoint_path)
                        logger.info(f"💾 保存检查点: Epoch {epoch + 1}")
                    except Exception as e:
                        logger.warning(f"⚠️ 保存检查点失败: {e}")

                # 强制输出每个epoch的结果
                print(f"📊 Epoch {epoch + 1}/{epochs} 完成 ({progress:.1f}%)")
                print(f"   训练损失: {avg_train_loss:.4f}, 验证损失: {avg_val_loss:.4f}")
                print(f"   训练准确率: {train_acc:.4f}, 验证准确率: {val_acc:.4f}")

                # 日志输出
                if (epoch + 1) % 5 == 0 or epoch == 0:  # 更频繁的日志输出
                    logger.info(f"Epoch {epoch + 1}/{epochs} ({progress:.1f}%): "
                              f"Train Loss: {avg_train_loss:.4f}, "
                              f"Val Loss: {avg_val_loss:.4f}, "
                              f"Train Acc: {train_acc:.4f}, "
                              f"Val Acc: {val_acc:.4f}")

                    # 输出GPU内存使用情况（如果使用GPU）
                    if self.device.type == 'cuda':
                        memory_allocated = torch.cuda.memory_allocated(0) / 1024**3
                        memory_reserved = torch.cuda.memory_reserved(0) / 1024**3
                        logger.info(f"GPU内存: 已分配 {memory_allocated:.2f}GB, 已保留 {memory_reserved:.2f}GB")
                        print(f"🖥️ GPU内存: {memory_allocated:.2f}GB / {memory_reserved:.2f}GB")

                # 学习率调度
                scheduler.step(avg_val_loss)

                # 重置重试计数器（成功完成一个epoch）
                retry_count = 0

            except Exception as e:
                logger.error(f"❌ Epoch {epoch + 1} 训练异常: {e}")
                retry_count += 1

                if retry_count <= max_retries:
                    logger.info(f"🔄 尝试重启训练 ({retry_count}/{max_retries})")

                    # 清理GPU内存
                    if self.device.type == 'cuda':
                        torch.cuda.empty_cache()

                    # 重新加载最后的检查点
                    if os.path.exists(latest_checkpoint_path):
                        try:
                            checkpoint = torch.load(latest_checkpoint_path, map_location=self.device)
                            model.load_state_dict(checkpoint['model_state_dict'])
                            optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
                            logger.info("✅ 从检查点恢复模型状态")
                        except Exception as restore_e:
                            logger.error(f"❌ 检查点恢复失败: {restore_e}")

                    time.sleep(5)  # 等待5秒后重试
                    continue
                else:
                    logger.error(f"❌ 训练失败，已达到最大重试次数 ({max_retries})")
                    self._update_task_status(task_id, 'failed', error=f"训练异常: {str(e)}")
                    return history

        return history
    
    def _evaluate_model(self, model: nn.Module, X_val: np.ndarray, y_val: np.ndarray) -> Dict[str, Any]:
        """评估模型性能"""
        model.eval()
        
        val_dataset = TradingDataset(X_val, y_val)
        val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False)
        
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for batch_X, batch_y in val_loader:
                batch_X = batch_X.to(self.device)
                outputs = model(batch_X).squeeze()
                predictions = torch.sigmoid(outputs).cpu().numpy()
                
                all_predictions.extend(predictions)
                all_targets.extend(batch_y.numpy())
        
        all_predictions = np.array(all_predictions)
        all_targets = np.array(all_targets)
        
        # 计算性能指标
        predicted_classes = (all_predictions > 0.5).astype(int)
        accuracy = (predicted_classes == all_targets).mean()
        
        # 计算精确率、召回率等
        tp = ((predicted_classes == 1) & (all_targets == 1)).sum()
        fp = ((predicted_classes == 1) & (all_targets == 0)).sum()
        tn = ((predicted_classes == 0) & (all_targets == 0)).sum()
        fn = ((predicted_classes == 0) & (all_targets == 1)).sum()
        
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0
        f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
        
        return {
            'accuracy': float(accuracy),
            'precision': float(precision),
            'recall': float(recall),
            'f1_score': float(f1_score),
            'total_samples': len(all_targets)
        }
    
    def _update_task_status(self, task_id: str, status: str, **kwargs):
        """更新任务状态"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            update_fields = ['status = ?', 'updated_at = ?']
            update_values = [status, datetime.now().isoformat()]

            if 'started_at' in kwargs:
                update_fields.append('started_at = ?')
                update_values.append(kwargs['started_at'])

            if 'completed_at' in kwargs:
                update_fields.append('completed_at = ?')
                update_values.append(kwargs['completed_at'])

            # 处理日志参数 - 支持直接传入logs或error
            if 'logs' in kwargs:
                update_fields.append('logs = ?')
                update_values.append(kwargs['logs'])
            elif 'error' in kwargs:
                update_fields.append('logs = ?')
                update_values.append(json.dumps({'error': kwargs['error']}))

            update_values.append(task_id)

            cursor.execute(f'''
                UPDATE training_tasks
                SET {', '.join(update_fields)}
                WHERE id = ?
            ''', update_values)

            conn.commit()
            conn.close()

            logger.debug(f"✅ 任务状态已更新: {task_id} -> {status}")

        except Exception as e:
            logger.error(f"❌ 更新任务状态失败: {e}")
    
    def _update_task_progress(self, task_id: str, progress: float, epoch: int,
                            train_loss: float, val_loss: float):
        """更新任务进度并触发回调（增强版本，包含详细监控）"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 强制刷新进度更新
            current_time = datetime.now().isoformat()
            cursor.execute('''
                UPDATE training_tasks
                SET progress = ?, current_epoch = ?, train_loss = ?, val_loss = ?, updated_at = ?
                WHERE id = ?
            ''', (progress, epoch, train_loss, val_loss, current_time, task_id))

            # 确保提交成功
            conn.commit()

            # 验证更新是否成功
            cursor.execute('SELECT progress, current_epoch, updated_at FROM training_tasks WHERE id = ?', (task_id,))
            result = cursor.fetchone()

            if result:
                actual_progress, actual_epoch, actual_updated = result
                logger.info(f"✅ 进度更新验证: {actual_progress}% (轮次 {actual_epoch}) 时间 {actual_updated}")

                # 强制输出到控制台
                print(f"🔄 训练进度: {actual_progress}% (轮次 {actual_epoch}/{epoch})")
            else:
                logger.error(f"❌ 进度更新验证失败: 任务 {task_id} 不存在")
                print(f"❌ 进度更新失败: 任务不存在")

            conn.close()

            # 记录详细的训练指标
            self._record_training_metrics(task_id, {
                'progress': progress,
                'epoch': epoch,
                'train_loss': train_loss,
                'val_loss': val_loss,
                'timestamp': datetime.now().isoformat(),
                'system_info': {
                    'cpu_percent': self.system_monitor.get('cpu_percent', 0),
                    'memory_percent': self.system_monitor.get('memory_percent', 0),
                    'gpu_info': self.system_monitor.get('gpu_info', {})
                }
            })

            # 新增：实时进度回调
            self._training_callback(task_id, "progress", progress, train_loss, val_loss)

        except Exception as e:
            logger.error(f"❌ 更新任务进度失败: {e}")
            # 新增：错误回调
            self._training_callback(task_id, "error", progress, train_loss, val_loss, str(e))

    def _record_training_metrics(self, task_id: str, metrics: Dict[str, Any]):
        """记录训练指标到文件"""
        try:
            metrics_dir = os.path.join(self.models_path, 'metrics')
            if not os.path.exists(metrics_dir):
                os.makedirs(metrics_dir)

            metrics_file = os.path.join(metrics_dir, f"{task_id}_metrics.jsonl")

            with open(metrics_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(metrics) + '\n')

        except Exception as e:
            logger.error(f"记录训练指标失败: {e}")

    def get_training_metrics(self, task_id: str) -> List[Dict[str, Any]]:
        """获取训练指标历史"""
        try:
            metrics_file = os.path.join(self.models_path, 'metrics', f"{task_id}_metrics.jsonl")

            if not os.path.exists(metrics_file):
                return []

            metrics = []
            with open(metrics_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if line.strip():
                        metrics.append(json.loads(line.strip()))

            return metrics

        except Exception as e:
            logger.error(f"获取训练指标失败: {e}")
            return []
    
    def get_training_progress(self, task_id: str) -> Dict[str, Any]:
        """获取训练进度"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT status, progress, current_epoch, total_epochs,
                       train_loss, val_loss, best_loss, logs, updated_at, model_id
                FROM training_tasks
                WHERE id = ?
            ''', (task_id,))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                # 解析日志信息
                logs_data = {}
                if result[7]:  # logs字段
                    try:
                        logs_data = json.loads(result[7])
                    except:
                        logs_data = {}

                return {
                    'success': True,
                    'progress': {
                        'status': result[0],
                        'progress': result[1],
                        'epoch': result[2],
                        'total_epochs': result[3],
                        'train_loss': result[4],
                        'val_loss': result[5],
                        'best_loss': result[6],
                        'logs': logs_data,
                        'updated_at': result[8],
                        'model_id': result[9]
                    }
                }
            else:
                return {'success': False, 'error': '任务不存在'}
                
        except Exception as e:
            logger.error(f"❌ 获取训练进度失败: {e}")
            return {'success': False, 'error': str(e)}

    def _check_training_timeout(self):
        """检查训练超时"""
        if hasattr(self, 'training_start_time') and hasattr(self, 'max_training_time'):
            if time.time() - self.training_start_time > self.max_training_time:
                logger.error("❌ 训练超时，自动停止")
                raise TimeoutError("训练超时")

    def check_and_fix_stuck_tasks(self) -> Dict[str, Any]:
        """检查并修复卡住的任务"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 查找可能卡住的任务（running状态但很久没更新）
            cursor.execute('''
                SELECT id, model_id, status, progress, updated_at
                FROM training_tasks
                WHERE status IN ('running', 'data_preparation')
                AND datetime(updated_at) < datetime('now', '-30 minutes')
            ''')

            stuck_tasks = cursor.fetchall()
            fixed_count = 0

            for task_id, model_id, status, progress, updated_at in stuck_tasks:
                logger.warning(f"⚠️ 发现卡住的任务: {task_id}, 状态: {status}, 进度: {progress}%")

                # 根据进度决定修复方式
                if progress >= 25:
                    # 如果进度>=25%，可能数据准备已完成，标记为data_ready
                    cursor.execute('''
                        UPDATE training_tasks
                        SET status = 'data_ready',
                            progress = 100,
                            logs = ?,
                            updated_at = ?
                        WHERE id = ?
                    ''', (
                        json.dumps({
                            'stage': 'data_ready',
                            'message': '数据准备完成，可以开始模型训练（自动修复）'
                        }),
                        datetime.now().isoformat(),
                        task_id
                    ))

                    # 更新模型状态
                    cursor.execute('''
                        UPDATE deep_learning_models
                        SET status = 'data_ready'
                        WHERE id = ?
                    ''', (model_id,))

                    logger.info(f"✅ 任务 {task_id} 已自动修复为data_ready状态")
                else:
                    # 否则标记为失败
                    cursor.execute('''
                        UPDATE training_tasks
                        SET status = 'failed',
                            logs = ?,
                            updated_at = ?
                        WHERE id = ?
                    ''', (
                        json.dumps({
                            'stage': 'failed',
                            'message': '任务卡住超时，自动标记为失败'
                        }),
                        datetime.now().isoformat(),
                        task_id
                    ))

                    logger.info(f"✅ 任务 {task_id} 已自动标记为失败")

                fixed_count += 1

            conn.commit()
            conn.close()

            return {
                'success': True,
                'fixed_count': fixed_count,
                'message': f'检查并修复了 {fixed_count} 个卡住的任务'
            }

        except Exception as e:
            logger.error(f"❌ 检查修复卡住任务失败: {e}")
            return {'success': False, 'error': str(e)}

    def get_training_tasks(self, user_id: int = None) -> Dict[str, Any]:
        """获取训练任务列表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            if user_id:
                cursor.execute('''
                    SELECT t.id, m.name, m.model_type, t.status, t.progress, 
                           t.created_at, t.completed_at
                    FROM training_tasks t
                    JOIN deep_learning_models m ON t.model_id = m.id
                    WHERE m.user_id = ?
                    ORDER BY t.created_at DESC
                ''', (user_id,))
            else:
                cursor.execute('''
                    SELECT t.id, m.name, m.model_type, t.status, t.progress, 
                           t.created_at, t.completed_at
                    FROM training_tasks t
                    JOIN deep_learning_models m ON t.model_id = m.id
                    ORDER BY t.created_at DESC
                ''')
            
            tasks = []
            for row in cursor.fetchall():
                tasks.append({
                    'id': row[0],
                    'name': row[1],
                    'model_type': row[2],
                    'status': row[3],
                    'progress': row[4],
                    'created_at': row[5],
                    'completed_at': row[6]
                })
            
            conn.close()
            
            return {'success': True, 'tasks': tasks}
            
        except Exception as e:
            logger.error(f"❌ 获取训练任务失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        try:
            import sys
            import psutil
            
            info = {
                'python_version': sys.version.split()[0],
                'pytorch_version': torch.__version__ if TORCH_AVAILABLE else None,
                'tensorflow_version': tf.__version__ if TF_AVAILABLE else None,
                'cuda_version': torch.version.cuda if TORCH_AVAILABLE and torch.cuda.is_available() else None,
                'available_memory': round(psutil.virtual_memory().available / 1024**3, 1),
                'cpu_count': psutil.cpu_count(),
                'device': str(self.device)
            }
            
            return {'success': True, 'info': info}
            
        except Exception as e:
            logger.error(f"❌ 获取系统信息失败: {e}")
            return {'success': False, 'error': str(e)}

    def get_models(self, user_id: int = None) -> Dict[str, Any]:
        """获取模型列表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            if user_id:
                cursor.execute('''
                    SELECT id, name, model_type, symbol, timeframe, status,
                           performance_metrics, created_at, completed_at
                    FROM deep_learning_models
                    WHERE user_id = ?
                    ORDER BY created_at DESC
                ''', (user_id,))
            else:
                cursor.execute('''
                    SELECT id, name, model_type, symbol, timeframe, status,
                           performance_metrics, created_at, completed_at
                    FROM deep_learning_models
                    ORDER BY created_at DESC
                ''')

            models = []
            for row in cursor.fetchall():
                performance = {}
                try:
                    if row[6]:  # performance_metrics
                        performance = json.loads(row[6])
                except:
                    pass

                models.append({
                    'id': row[0],
                    'name': row[1],
                    'model_type': row[2],
                    'symbol': row[3],
                    'timeframe': row[4],
                    'status': row[5],
                    'performance': performance,
                    'created_at': row[7],
                    'completed_at': row[8]
                })

            conn.close()

            return {'success': True, 'models': models}

        except Exception as e:
            logger.error(f"❌ 获取模型列表失败: {e}")
            return {'success': False, 'error': str(e)}

    def get_model_detail(self, model_id: str, user_id: int = None) -> Dict[str, Any]:
        """获取模型详情"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            if user_id:
                cursor.execute('''
                    SELECT id, name, model_type, symbol, timeframe, config,
                           training_history, performance_metrics, status,
                           created_at, completed_at, model_path
                    FROM deep_learning_models
                    WHERE id = ? AND user_id = ?
                ''', (model_id, user_id))
            else:
                cursor.execute('''
                    SELECT id, name, model_type, symbol, timeframe, config,
                           training_history, performance_metrics, status,
                           created_at, completed_at, model_path
                    FROM deep_learning_models
                    WHERE id = ?
                ''', (model_id,))

            result = cursor.fetchone()
            conn.close()

            if not result:
                return {'success': False, 'error': '模型不存在'}

            # 解析JSON数据
            config = {}
            training_history = {}
            performance = {}

            try:
                if result[5]:  # config
                    config = json.loads(result[5])
                if result[6]:  # training_history
                    training_history = json.loads(result[6])
                if result[7]:  # performance_metrics
                    performance = json.loads(result[7])
            except:
                pass

            # 计算模型文件大小
            model_size = 0
            model_size_mb = 0
            if result[11]:  # model_path
                try:
                    import os
                    if os.path.exists(result[11]):
                        model_size = os.path.getsize(result[11])
                        model_size_mb = model_size / (1024 * 1024)
                except:
                    pass

            # 获取训练数据信息
            data_info = self._get_training_data_info(config, result[0])

            model_detail = {
                'id': result[0],
                'name': result[1],
                'model_type': result[2],
                'symbol': result[3],
                'timeframe': result[4],
                'config': config,
                'training_history': training_history,
                'performance': performance,
                'status': result[8],
                'created_at': result[9],
                'completed_at': result[10],
                'model_path': result[11],
                'model_size_bytes': model_size,
                'model_size_mb': model_size_mb,
                'data_info': data_info
            }

            return {'success': True, 'model': model_detail}

        except Exception as e:
            logger.error(f"❌ 获取模型详情失败: {e}")
            return {'success': False, 'error': str(e)}

    def _get_training_data_info(self, config: Dict, model_id: str) -> Dict[str, Any]:
        """获取训练数据信息"""
        try:
            data_info = {
                'start_date': None,
                'end_date': None,
                'total_samples': 0,
                'training_samples': 0,
                'validation_samples': 0,
                'features_used': [],
                'data_quality': 'unknown'
            }

            # 从配置中获取基本信息
            if config:
                data_info['start_date'] = config.get('start_date')
                data_info['end_date'] = config.get('end_date')

                # 设置默认特征
                features = config.get('features', ['open', 'high', 'low', 'close', 'volume'])
                data_info['features_used'] = features

                # 获取训练参数
                validation_split = config.get('validation_split', 0.2)
                sequence_length = config.get('sequence_length', 60)
                batch_size = config.get('batch_size', 32)
                epochs = config.get('epochs', 100)

                # 尝试从训练任务日志中获取实际数据信息
                try:
                    conn = sqlite3.connect(self.db_path)
                    cursor = conn.cursor()

                    cursor.execute('''
                        SELECT logs FROM training_tasks
                        WHERE model_id = ?
                        ORDER BY created_at DESC
                        LIMIT 1
                    ''', (model_id,))

                    task_result = cursor.fetchone()
                    if task_result and task_result[0]:
                        logs = json.loads(task_result[0])

                        # 从日志中提取数据信息
                        if 'data_info' in logs:
                            data_info.update(logs['data_info'])

                        # 从训练日志中提取样本数量信息
                        if 'training_info' in logs:
                            training_info = logs['training_info']
                            if 'total_samples' in training_info:
                                data_info['total_samples'] = training_info['total_samples']
                            if 'training_samples' in training_info:
                                data_info['training_samples'] = training_info['training_samples']
                            if 'validation_samples' in training_info:
                                data_info['validation_samples'] = training_info['validation_samples']

                    conn.close()
                except Exception as e:
                    logger.warning(f"⚠️ 从训练日志获取数据信息失败: {e}")

                # 如果没有从日志中获取到样本数量，尝试估算
                if data_info['total_samples'] == 0 and data_info['start_date'] and data_info['end_date']:
                    try:
                        # 根据时间范围估算数据量
                        from datetime import datetime
                        start_dt = datetime.strptime(data_info['start_date'], '%Y-%m-%d')
                        end_dt = datetime.strptime(data_info['end_date'], '%Y-%m-%d')
                        days = (end_dt - start_dt).days

                        # 根据时间框架估算数据点数量
                        timeframe = config.get('timeframe', '1h')
                        if timeframe == '1h':
                            estimated_points = days * 24  # 每天24小时
                        elif timeframe == '4h':
                            estimated_points = days * 6   # 每天6个4小时周期
                        elif timeframe == '1d':
                            estimated_points = days       # 每天1个数据点
                        elif timeframe == '15m':
                            estimated_points = days * 96  # 每天96个15分钟周期
                        else:
                            estimated_points = days * 24  # 默认按小时计算

                        # 考虑序列长度的影响
                        if estimated_points > sequence_length:
                            data_info['total_samples'] = estimated_points - sequence_length + 1
                            data_info['training_samples'] = int(data_info['total_samples'] * (1 - validation_split))
                            data_info['validation_samples'] = data_info['total_samples'] - data_info['training_samples']

                            # 根据数据量评估质量
                            if data_info['total_samples'] > 10000:
                                data_info['data_quality'] = 'good'
                            elif data_info['total_samples'] > 5000:
                                data_info['data_quality'] = 'fair'
                            else:
                                data_info['data_quality'] = 'poor'

                    except Exception as e:
                        logger.warning(f"⚠️ 估算数据量失败: {e}")

            return data_info

        except Exception as e:
            logger.error(f"❌ 获取训练数据信息失败: {e}")
            return {
                'start_date': None,
                'end_date': None,
                'total_samples': 0,
                'training_samples': 0,
                'validation_samples': 0,
                'features_used': [],
                'data_quality': 'unknown'
            }

    def delete_model(self, model_id: str, user_id: int = None) -> Dict[str, Any]:
        """删除模型"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 首先获取模型信息
            if user_id:
                cursor.execute('''
                    SELECT model_path FROM deep_learning_models
                    WHERE id = ? AND user_id = ?
                ''', (model_id, user_id))
            else:
                cursor.execute('''
                    SELECT model_path FROM deep_learning_models
                    WHERE id = ?
                ''', (model_id,))

            result = cursor.fetchone()
            if not result:
                conn.close()
                return {'success': False, 'error': '模型不存在'}

            model_path = result[0]

            # 删除数据库记录
            if user_id:
                cursor.execute('''
                    DELETE FROM deep_learning_models
                    WHERE id = ? AND user_id = ?
                ''', (model_id, user_id))
            else:
                cursor.execute('''
                    DELETE FROM deep_learning_models
                    WHERE id = ?
                ''', (model_id,))

            # 删除相关的训练任务
            cursor.execute('''
                DELETE FROM training_tasks
                WHERE model_id = ?
            ''', (model_id,))

            conn.commit()
            conn.close()

            # 删除模型文件
            if model_path and os.path.exists(model_path):
                try:
                    os.remove(model_path)
                    logger.info(f"✅ 已删除模型文件: {model_path}")
                except Exception as e:
                    logger.warning(f"⚠️ 删除模型文件失败: {e}")

            logger.info(f"✅ 模型删除成功: {model_id}")
            return {'success': True, 'message': '模型删除成功'}

        except Exception as e:
            logger.error(f"❌ 删除模型失败: {e}")
            return {'success': False, 'error': str(e)}

    def pause_training(self, task_id: str) -> Dict[str, Any]:
        """暂停训练"""
        try:
            # 首先检查任务是否在数据库中且为运行状态
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT status FROM training_tasks WHERE id = ?
            ''', (task_id,))

            task = cursor.fetchone()
            conn.close()

            if not task:
                return {'success': False, 'error': '训练任务不存在'}

            if task[0] not in ['running', 'pending']:
                return {'success': False, 'error': f'训练任务已{task[0]}，无法暂停'}

            # 如果任务不在控制字典中，重新注册
            if task_id not in self.training_control:
                logger.info(f"🔧 重新注册训练控制状态: {task_id}")
                self.training_control[task_id] = {'stop': False, 'pause': False}

            # 设置暂停标志
            self.training_control[task_id]['pause'] = True
            logger.info(f"⏸️ 训练暂停请求已发送: {task_id}")

            # 更新数据库状态为暂停
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE training_tasks
                SET status = 'paused', updated_at = ?
                WHERE id = ?
            ''', (datetime.now().isoformat(), task_id))
            conn.commit()
            conn.close()

            return {'success': True, 'message': '训练暂停请求已发送'}

        except Exception as e:
            logger.error(f"❌ 暂停训练失败: {e}")
            return {'success': False, 'error': str(e)}

    def resume_training(self, task_id: str) -> Dict[str, Any]:
        """恢复训练"""
        try:
            # 首先检查任务是否在数据库中且为暂停状态
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT status FROM training_tasks WHERE id = ?
            ''', (task_id,))

            task = cursor.fetchone()
            conn.close()

            if not task:
                return {'success': False, 'error': '训练任务不存在'}

            if task[0] not in ['paused', 'running']:
                return {'success': False, 'error': f'训练任务已{task[0]}，无法恢复'}

            # 如果任务不在控制字典中，重新注册
            if task_id not in self.training_control:
                logger.info(f"🔧 重新注册训练控制状态: {task_id}")
                self.training_control[task_id] = {'stop': False, 'pause': False}

            # 清除暂停标志
            self.training_control[task_id]['pause'] = False
            logger.info(f"▶️ 训练恢复请求已发送: {task_id}")

            # 更新数据库状态为运行
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE training_tasks
                SET status = 'running', updated_at = ?
                WHERE id = ?
            ''', (datetime.now().isoformat(), task_id))
            conn.commit()
            conn.close()

            return {'success': True, 'message': '训练恢复请求已发送'}

        except Exception as e:
            logger.error(f"❌ 恢复训练失败: {e}")
            return {'success': False, 'error': str(e)}

    def stop_training(self, task_id: str) -> Dict[str, Any]:
        """停止训练"""
        try:
            # 首先检查任务是否在数据库中
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT status FROM training_tasks WHERE id = ?
            ''', (task_id,))

            task = cursor.fetchone()
            conn.close()

            if not task:
                return {'success': False, 'error': '训练任务不存在'}

            if task[0] in ['completed', 'failed', 'stopped']:
                return {'success': False, 'error': f'训练任务已{task[0]}，无法停止'}

            # 如果任务不在控制字典中，重新注册
            if task_id not in self.training_control:
                logger.info(f"🔧 重新注册训练控制状态: {task_id}")
                self.training_control[task_id] = {'stop': False, 'pause': False}

            # 设置停止标志
            self.training_control[task_id]['stop'] = True
            logger.info(f"🛑 训练停止请求已发送: {task_id}")

            # 更新数据库状态
            self._update_task_status(task_id, 'stopped')

            return {'success': True, 'message': '训练停止请求已发送'}

        except Exception as e:
            logger.error(f"❌ 停止训练失败: {e}")
            return {'success': False, 'error': str(e)}

    def get_training_control_status(self, task_id: str) -> Dict[str, Any]:
        """获取训练控制状态"""
        try:
            # 检查数据库中的任务状态
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT status FROM training_tasks WHERE id = ?
            ''', (task_id,))

            task = cursor.fetchone()
            conn.close()

            if not task:
                return {
                    'success': True,
                    'control': {
                        'is_paused': False,
                        'is_stopped': False,
                        'can_control': False,
                        'reason': '任务不存在'
                    }
                }

            task_status = task[0]

            # 如果任务在内存控制字典中
            if task_id in self.training_control:
                control_state = self.training_control[task_id]
                return {
                    'success': True,
                    'control': {
                        'is_paused': control_state.get('pause', False),
                        'is_stopped': control_state.get('stop', False),
                        'can_control': True,
                        'task_status': task_status
                    }
                }
            else:
                # 根据数据库状态判断是否可控制
                can_control = task_status in ['running', 'paused', 'pending']

                return {
                    'success': True,
                    'control': {
                        'is_paused': task_status == 'paused',
                        'is_stopped': task_status in ['stopped', 'failed', 'completed'],
                        'can_control': can_control,
                        'task_status': task_status,
                        'reason': '任务不在内存控制中' if not can_control else None
                    }
                }
        except Exception as e:
            logger.error(f"❌ 获取训练控制状态失败: {e}")
            return {'success': False, 'error': str(e)}

    def _calculate_training_days(self, data_config: Dict[str, Any], fallback_config: Dict[str, Any]) -> int:
        """计算训练数据天数"""
        try:
            mode = data_config.get('mode', 'days')

            if mode == 'days':
                # 按天数模式
                days = data_config.get('training_days', fallback_config.get('training_days', 365))
                logger.info(f"📅 使用天数模式: {days}天")
                return days

            elif mode == 'range':
                # 日期范围模式
                start_date_str = data_config.get('start_date')
                end_date_str = data_config.get('end_date')

                if start_date_str and end_date_str:
                    start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
                    end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
                    days = (end_date - start_date).days

                    logger.info(f"📅 使用日期范围模式: {start_date_str} 到 {end_date_str} ({days}天)")
                    return max(days, 1)  # 至少1天
                else:
                    logger.warning("⚠️ 日期范围模式缺少日期参数，使用默认值")
                    return fallback_config.get('training_days', 365)

            elif mode == 'preset':
                # 预设范围模式
                preset = data_config.get('preset_range')
                days = self._get_preset_days(preset)

                logger.info(f"📅 使用预设范围模式: {preset} ({days}天)")
                return days

            else:
                logger.warning(f"⚠️ 未知的日期模式: {mode}，使用默认值")
                return fallback_config.get('training_days', 365)

        except Exception as e:
            logger.error(f"❌ 计算训练天数失败: {e}")
            return fallback_config.get('training_days', 365)

    def _get_preset_days(self, preset: str) -> int:
        """获取预设范围的天数"""
        preset_days = {
            '1m': 30,
            '3m': 90,
            '6m': 180,
            '1y': 365,
            '2y': 730,
            '3y': 1095,
            '5y': 1825,
            'ytd': self._get_ytd_days(),
            'last_year': 365,
            'bull_market': 730,  # 2020-2021大约2年
            'bear_market': 365,  # 2022年
            'covid_period': 275  # 2020年3月-12月大约9个月
        }

        return preset_days.get(preset, 365)

    def _get_ytd_days(self) -> int:
        """获取今年至今的天数"""
        today = datetime.now()
        year_start = datetime(today.year, 1, 1)
        return (today - year_start).days + 1

    def _get_date_range_info(self, data_config: Dict[str, Any]) -> Dict[str, Any]:
        """获取日期范围信息（用于日志和调试）"""
        try:
            mode = data_config.get('mode', 'days')

            if mode == 'days':
                days = data_config.get('training_days', 365)
                end_date_str = data_config.get('end_date')

                if end_date_str:
                    end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
                else:
                    end_date = datetime.now()

                start_date = end_date - timedelta(days=days)

                return {
                    'start_date': start_date.strftime('%Y-%m-%d'),
                    'end_date': end_date.strftime('%Y-%m-%d'),
                    'days': days,
                    'mode': 'days'
                }

            elif mode == 'range':
                return {
                    'start_date': data_config.get('start_date'),
                    'end_date': data_config.get('end_date'),
                    'days': self._calculate_training_days(data_config, {}),
                    'mode': 'range'
                }

            elif mode == 'preset':
                preset = data_config.get('preset_range')
                days = self._get_preset_days(preset)
                end_date = datetime.now()
                start_date = end_date - timedelta(days=days)

                return {
                    'start_date': start_date.strftime('%Y-%m-%d'),
                    'end_date': end_date.strftime('%Y-%m-%d'),
                    'days': days,
                    'mode': 'preset',
                    'preset': preset
                }

        except Exception as e:
            logger.error(f"❌ 获取日期范围信息失败: {e}")

        # 默认返回
        return {
            'start_date': (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d'),
            'end_date': datetime.now().strftime('%Y-%m-%d'),
            'days': 365,
            'mode': 'default'
        }

    def _training_callback(self, task_id: str, status: str, progress: float,
                          train_loss: float, val_loss: float, error: str = None):
        """训练进度回调函数（供外部系统订阅）"""
        # 实际项目中这里会连接消息队列或WebSocket
        # 简化版：直接打印日志
        log_msg = f"📢 训练回调: 任务={task_id}, 状态={status}, 进度={progress}%"
        if status == "progress":
            log_msg += f", 训练损失={train_loss:.4f}, 验证损失={val_loss:.4f}"
        elif status == "error":
            log_msg += f", 错误={error}"
        
        logger.info(log_msg)
        
        # 实际实现示例（伪代码）：
        # websocket.broadcast(f"training/{task_id}", {
        #     'status': status,
        #     'progress': progress,
        #     'train_loss': train_loss,
        #     'val_loss': val_loss,
        #     'error': error
        # })

    def run_inference(self, model_id: str, user_id: int, symbol: str, timeframe: str,
                     inference_mode: str, data_points: int = 100, start_date: str = None,
                     end_date: str = None, use_gpu: bool = True, show_confidence: bool = True,
                     trade_config: Dict = None) -> Dict[str, Any]:
        """运行深度学习模型推理"""
        try:
            logger.info(f"🔮 开始深度学习推理: 模型={model_id[:8]}..., 模式={inference_mode}")

            # 1. 验证模型存在
            model_detail = self.get_model_detail(model_id, user_id)
            if not model_detail['success']:
                return {'success': False, 'error': '模型不存在或无权限访问'}

            model = model_detail['model']
            if model['status'] != 'completed':
                return {'success': False, 'error': '模型未完成训练，无法进行推理'}

            # 2. 根据推理模式准备数据
            if inference_mode == 'realtime':
                # 实时推理：获取最新数据
                logger.info(f"📡 实时推理模式：获取 {symbol} 最新 {data_points} 个数据点")
                inference_data = self._get_realtime_data(symbol, timeframe, data_points)
                time_info = "实时数据"
            else:
                # 历史推理：获取指定时间范围数据
                logger.info(f"📊 历史推理模式：{start_date} 至 {end_date}")
                inference_data = self._get_historical_data(symbol, timeframe, start_date, end_date, data_points)
                time_info = f"{start_date} 至 {end_date}"

            if not inference_data['success']:
                return {'success': False, 'error': f'获取推理数据失败: {inference_data["error"]}'}

            # 3. 执行推理
            logger.info(f"🧠 执行模型推理...")
            inference_result = self._execute_inference(
                model=model,
                data=inference_data['data'],
                use_gpu=use_gpu,
                show_confidence=show_confidence
            )

            if not inference_result['success']:
                return {'success': False, 'error': f'推理执行失败: {inference_result["error"]}'}

            # 4. 格式化推理结果
            result = {
                'success': True,
                'inference_id': f"inf_{int(time.time())}",
                'model_info': {
                    'id': model_id,
                    'name': model['name'],
                    'type': model['model_type'],
                    'symbol': model['symbol'],
                    'timeframe': model['timeframe']
                },
                'inference_config': {
                    'mode': inference_mode,
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'data_points': data_points,
                    'time_range': time_info,
                    'use_gpu': use_gpu,
                    'show_confidence': show_confidence
                },
                'trade_config': trade_config or {
                    'trade_size': 0.01,
                    'min_confidence': 0.1,
                    'stop_loss_pips': 50,
                    'take_profit_pips': 100,
                    'trade_mode': 'signal_only',
                    'dynamic_sl': True,
                    'trailing_stop': False
                },
                'results': inference_result['results'],
                'metadata': {
                    'inference_time': datetime.now().isoformat(),
                    'data_samples': len(inference_data['data']),
                    'processing_time': inference_result.get('processing_time', 0)
                }
            }

            logger.info(f"✅ 推理完成: {len(inference_result['results'])} 个预测结果")
            return result

        except Exception as e:
            logger.error(f"❌ 深度学习推理失败: {e}")
            return {'success': False, 'error': str(e)}

    def _get_realtime_data(self, symbol: str, timeframe: str, data_points: int) -> Dict[str, Any]:
        """获取实时数据用于推理"""
        try:
            logger.info(f"📡 获取 {symbol} 实时数据...")

            # 尝试从MT5获取真实数据
            try:
                from services.mt5_service import mt5_service

                # 检查MT5连接状态
                connection_status = mt5_service.get_connection_status()
                if connection_status.get('connected', False):
                    logger.info(f"🔗 使用MT5获取 {symbol} 实时数据")

                    # 获取MT5历史数据
                    mt5_data = mt5_service.get_historical_data(
                        symbol=symbol,
                        timeframe=timeframe,
                        count=data_points
                    )

                    if mt5_data and len(mt5_data) > 0:
                        # 转换MT5数据格式
                        data = []
                        for bar in mt5_data:
                            data.append({
                                'timestamp': bar['time'],  # MT5Service已经转换为datetime
                                'open': bar['open'],
                                'high': bar['high'],
                                'low': bar['low'],
                                'close': bar['close'],
                                'volume': bar['volume']
                            })

                        logger.info(f"✅ 成功获取 {len(data)} 个MT5数据点")
                        return {
                            'success': True,
                            'data': data,
                            'source': 'mt5_realtime',
                            'count': len(data)
                        }
                    else:
                        logger.error(f"❌ MT5数据获取失败，系统严禁使用模拟数据")
                        return {
                            'success': False,
                            'error': 'MT5数据获取失败，系统严禁使用模拟数据。请检查MT5连接和数据源。',
                            'source': 'error'
                        }
                else:
                    logger.error(f"❌ MT5未连接，系统严禁使用模拟数据")
                    return {
                        'success': False,
                        'error': 'MT5未连接，系统严禁使用模拟数据。请确保MT5正常连接。',
                        'source': 'error'
                    }

            except Exception as mt5_error:
                logger.error(f"❌ MT5数据获取异常: {mt5_error}，系统严禁使用模拟数据")
                return {
                    'success': False,
                    'error': f'MT5数据获取异常: {mt5_error}，系统严禁使用模拟数据。',
                    'source': 'error'
                }

        except Exception as e:
            logger.error(f"❌ 获取实时数据失败: {e}")
            return {'success': False, 'error': str(e)}

    def _get_historical_data(self, symbol: str, timeframe: str, start_date: str,
                           end_date: str, data_points: int) -> Dict[str, Any]:
        """获取历史数据用于推理"""
        try:
            logger.info(f"📊 获取 {symbol} 历史数据: {start_date} 至 {end_date}")

            # 首先检查并确保MT5连接正常
            logger.info(f"🔍 开始获取推理数据前，检查MT5连接状态...")
            if not self._check_and_reconnect_mt5(symbol):
                logger.warning("⚠️ MT5连接失败，将使用备用数据源")

            # 尝试从MT5获取真实历史数据
            try:
                from services.mt5_service import mt5_service

                # 检查MT5连接状态
                connection_status = mt5_service.get_connection_status()
                if connection_status.get('connected', False):
                    logger.info(f"🔗 使用MT5获取 {symbol} 历史数据")

                    # 转换日期格式
                    start_dt = datetime.strptime(start_date, '%Y-%m-%d')
                    end_dt = datetime.strptime(end_date, '%Y-%m-%d')

                    # 获取MT5历史数据（修复时间范围问题）
                    logger.info(f"🔍 从MT5获取数据: {symbol} {timeframe} {start_date}至{end_date}")

                    # 验证参数
                    if not symbol or not symbol.strip():
                        logger.error(f"❌ 交易品种为空: '{symbol}'")
                        return {
                            'success': False,
                            'error': f'交易品种参数为空，请检查模型配置',
                            'source': 'error'
                        }

                    print(f"🔍 使用时间范围获取: {start_date} 至 {end_date}")

                    # 使用时间范围获取数据，而不是固定数量
                    mt5_data = mt5_service.get_historical_data(
                        symbol=symbol,
                        timeframe=timeframe,
                        start_date=start_dt,
                        end_date=end_dt
                    )

                    if mt5_data and len(mt5_data) > 0:
                        # 转换MT5数据格式
                        data = []
                        for bar in mt5_data:
                            data.append({
                                'timestamp': bar['time'],  # MT5Service已经转换为datetime
                                'open': bar['open'],
                                'high': bar['high'],
                                'low': bar['low'],
                                'close': bar['close'],
                                'volume': bar['volume']
                            })

                        logger.info(f"✅ 成功获取 {len(data)} 个MT5历史数据点")
                        return {
                            'success': True,
                            'data': data,
                            'source': 'mt5_historical',
                            'count': len(data),
                            'time_range': f"{start_date} 至 {end_date}"
                        }
                    else:
                        logger.error(f"❌ MT5历史数据获取失败，系统严禁使用模拟数据")
                        return {
                            'success': False,
                            'error': 'MT5历史数据获取失败，系统严禁使用模拟数据。请检查MT5连接和历史数据权限。',
                            'source': 'error'
                        }
                else:
                    logger.error(f"❌ MT5未连接，系统严禁使用模拟数据")
                    return {
                        'success': False,
                        'error': 'MT5未连接，系统严禁使用模拟数据。请确保MT5正常连接。',
                        'source': 'error'
                    }

            except Exception as mt5_error:
                logger.error(f"❌ MT5历史数据获取异常: {mt5_error}，系统严禁使用模拟数据")
                return {
                    'success': False,
                    'error': f'MT5历史数据获取异常: {mt5_error}，系统严禁使用模拟数据。',
                    'source': 'error'
                }

        except Exception as e:
            logger.error(f"❌ 获取历史数据失败: {e}")
            return {'success': False, 'error': str(e)}

    def _get_inference_data(self, symbol: str, timeframe: str, data_points: int = 100) -> Dict[str, Any]:
        """获取推理数据（为API调用提供）"""
        try:
            logger.info(f"🔍 获取推理数据: {symbol} {timeframe} {data_points}条")

            from services.mt5_service import mt5_service

            # 检查MT5连接状态
            connection_status = mt5_service.get_connection_status()
            if not connection_status.get('connected', False):
                logger.error(f"❌ MT5未连接")
                return {
                    'success': False,
                    'error': 'MT5未连接，无法获取推理数据',
                    'data': []
                }

            # 获取MT5历史数据
            logger.info(f"🔗 从MT5获取推理数据: {symbol} {timeframe}")

            # 获取更多数据以确保有足够的数据点
            request_count = max(data_points, 200)

            mt5_data = mt5_service.get_historical_data(
                symbol=symbol,
                timeframe=timeframe,
                count=request_count
            )

            if mt5_data and len(mt5_data) > 0:
                # 转换数据格式
                data = []
                for bar in mt5_data:
                    try:
                        data.append({
                            'timestamp': bar['time'],
                            'open': float(bar['open']),
                            'high': float(bar['high']),
                            'low': float(bar['low']),
                            'close': float(bar['close']),
                            'volume': int(bar.get('volume', 0))
                        })
                    except (KeyError, ValueError, TypeError) as e:
                        logger.warning(f"⚠️ 跳过无效数据点: {e}")
                        continue

                # 只返回请求的数据点数量
                if len(data) > data_points:
                    data = data[-data_points:]  # 取最新的数据点

                logger.info(f"✅ 成功获取 {len(data)} 个推理数据点")

                return {
                    'success': True,
                    'data': data,
                    'count': len(data),
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'source': 'mt5'
                }
            else:
                logger.error(f"❌ MT5推理数据获取失败")
                return {
                    'success': False,
                    'error': f'无法获取{symbol} {timeframe}的推理数据，请检查MT5连接和品种设置',
                    'data': []
                }

        except Exception as e:
            logger.error(f"❌ 获取推理数据异常: {e}")
            return {
                'success': False,
                'error': f'获取推理数据异常: {str(e)}',
                'data': []
            }

    def _execute_inference(self, model: Dict, data: List[Dict], use_gpu: bool,
                          show_confidence: bool) -> Dict[str, Any]:
        """执行模型推理"""
        try:
            import time
            start_time = time.time()

            logger.info(f"🧠 执行推理: 数据点={len(data)}, GPU={use_gpu}, 模型={model['name']}")

            # 验证数据
            if not data or len(data) == 0:
                return {'success': False, 'error': '推理数据为空'}

            # 尝试加载真实模型进行推理
            try:
                model_path = model.get('model_path')
                if model_path and os.path.exists(model_path):
                    logger.info(f"🔮 使用训练好的模型进行推理: {model_path}")

                    # 加载真实的PyTorch模型进行推理
                    results = self._load_and_run_pytorch_model(model_path, data, model, show_confidence, use_gpu)

                else:
                    logger.error(f"❌ 模型文件不存在: {model_path}，无法进行推理")
                    return {
                        'success': False,
                        'error': f'模型文件不存在: {model_path}，系统严禁使用模拟推理。',
                        'processing_time': 0
                    }

            except Exception as model_error:
                logger.error(f"❌ 模型加载失败: {model_error}，无法进行推理")
                return {
                    'success': False,
                    'error': f'模型加载失败: {model_error}，系统严禁使用模拟推理。',
                    'processing_time': 0
                }

            processing_time = time.time() - start_time

            if not results:
                return {'success': False, 'error': '推理未产生有效结果'}

            logger.info(f"✅ 推理完成: {len(results)} 个预测结果，耗时 {processing_time:.3f}秒")

            return {
                'success': True,
                'results': results,
                'processing_time': round(processing_time, 3),
                'model_used': model['name'],
                'gpu_used': use_gpu,
                'data_source': 'mt5' if any('mt5' in str(d) for d in data) else 'simulated'
            }

        except Exception as e:
            logger.error(f"❌ 执行推理失败: {e}")
            return {'success': False, 'error': str(e)}

    def _format_price(self, price: float, symbol: str) -> float:
        """根据交易品种格式化价格小数位"""
        try:
            if symbol == 'XAUUSD':  # 黄金保留2位小数
                return round(price, 2)
            elif 'JPY' in symbol:  # 日元对保留3位小数
                return round(price, 3)
            else:  # 其他货币对保留5位小数
                return round(price, 5)
        except:
            return round(price, 2)  # 默认保留2位小数

    def _validate_confidence(self, confidence: float) -> float:
        """验证并修正置信度，确保在0-1范围内"""
        try:
            # 确保置信度在0-1范围内
            confidence = max(0.0, min(1.0, confidence))
            # 如果置信度过高，限制在95%以内
            if confidence > 0.95:
                confidence = 0.95
            return confidence
        except:
            return 0.5  # 默认50%置信度

    def _intelligent_inference(self, data: List[Dict], model: Dict, show_confidence: bool) -> List[Dict]:
        """智能推理：基于价格模式分析"""
        try:
            import numpy as np

            results = []

            # 分析最近的价格数据
            recent_data = data[-20:] if len(data) >= 20 else data  # 使用最近20个数据点

            for i, data_point in enumerate(recent_data[-10:]):  # 对最后10个数据点进行预测
                # 初始化变量
                prediction = None
                confidence = 0.0
                price_target = 0.0

                try:
                    # 使用简化的数据分析方法
                    current_price = data_point.get('close', 0)

                    if current_price <= 0:
                        logger.warning(f"⚠️ 价格数据无效({current_price})，跳过预测")
                        continue

                    # 获取周围的价格数据用于计算波动性
                    actual_idx = len(recent_data) - 10 + i
                    start_idx = max(0, actual_idx - 5)
                    end_idx = min(len(recent_data), actual_idx + 6)

                    # 获取价格序列
                    prices = [d.get('close', 0) for d in recent_data[start_idx:end_idx] if d.get('close', 0) > 0]

                    # 如果价格数据不足，使用更多数据
                    if len(prices) < 3:
                        prices = [d.get('close', 0) for d in recent_data[-min(len(recent_data), 10):] if d.get('close', 0) > 0]

                    # 计算价格变化和波动性
                    if len(prices) >= 2:
                        # 计算价格变化
                        price_change = (prices[-1] - prices[0]) / prices[0] if prices[0] != 0 else 0

                        # 计算波动性
                        volatility = np.std(prices) / np.mean(prices) if np.mean(prices) != 0 else 0
                    else:
                        # 数据不足时的处理
                        prev_price = current_price

                        # 查找前一个有效价格
                        for j in range(len(recent_data) - 1, -1, -1):
                            prev_data = recent_data[j]
                            if (prev_data.get('close', 0) > 0 and
                                prev_data != data_point and
                                abs(j - actual_idx) <= 5):
                                prev_price = prev_data['close']
                                break

                        price_change = (current_price - prev_price) / prev_price if prev_price != 0 else 0
                        volatility = 0.01  # 默认波动性

                    # 基于价格变化生成预测 - 降低阈值增加交易机会
                    if abs(price_change) > 0.0001:  # 变化超过0.01%（降低阈值）
                        if price_change > 0:
                            prediction = 'BUY'
                            # 计算置信度并验证范围
                            raw_confidence = 0.2 + min(abs(price_change) * 50, 0.6)
                            confidence = self._validate_confidence(raw_confidence)
                            # 使用格式化函数处理目标价格
                            price_target = self._format_price(current_price * 1.002, data[0].get('symbol', 'XAUUSD'))
                        else:
                            prediction = 'SELL'
                            # 计算置信度并验证范围
                            raw_confidence = 0.2 + min(abs(price_change) * 50, 0.6)
                            confidence = self._validate_confidence(raw_confidence)
                            # 使用格式化函数处理目标价格
                            price_target = self._format_price(current_price * 0.998, data[0].get('symbol', 'XAUUSD'))
                    else:
                        # 即使价格变化很小，也可能基于波动性产生交易信号
                        if volatility > 0.005:  # 如果波动性较高
                            prediction = 'BUY' if price_change >= 0 else 'SELL'
                            # 计算置信度并验证范围
                            raw_confidence = 0.15 + min(volatility * 20, 0.3)
                            confidence = self._validate_confidence(raw_confidence)
                            # 使用格式化函数处理目标价格
                            multiplier = 1.001 if prediction == 'BUY' else 0.999
                            price_target = self._format_price(current_price * multiplier, data[0].get('symbol', 'XAUUSD'))
                        else:
                            prediction = 'HOLD'
                            # 计算置信度并验证范围
                            raw_confidence = 0.1 + min(volatility * 10, 0.15)
                            confidence = self._validate_confidence(raw_confidence)
                            # 使用格式化函数处理目标价格
                            price_target = self._format_price(current_price, data[0].get('symbol', 'XAUUSD'))

                except Exception as e:
                    logger.warning(f"⚠️ 预测计算异常: {e}，跳过此数据点")
                    continue

                # 生成预测说明
                if prediction == 'BUY':
                    prediction_reason = f"检测到{abs(price_change)*100:.1f}%上涨趋势，建议买入"
                elif prediction == 'SELL':
                    prediction_reason = f"检测到{abs(price_change)*100:.1f}%下跌趋势，建议卖出"
                else:  # HOLD
                    prediction_reason = f"价格变化{abs(price_change)*100:.1f}%，趋势不明显，建议观望"

                result = {
                    'index': i,
                    'timestamp': data_point['timestamp'].isoformat() if hasattr(data_point['timestamp'], 'isoformat') else str(data_point['timestamp']),
                    'current_price': round(data_point['close'], 5),
                    'prediction': prediction,
                    'price_target': round(price_target, 5),
                    'confidence': round(confidence, 3) if show_confidence else None,
                    'analysis': {
                        'price_change': round(price_change * 100, 2),  # 百分比
                        'volatility': round(volatility * 100, 2),
                        'trend': 'bullish' if price_change > 0.01 else 'bearish' if price_change < -0.01 else 'sideways',
                        'reason': prediction_reason,
                        'signal_strength': 'strong' if abs(price_change) > 0.02 else 'medium' if abs(price_change) > 0.01 else 'weak'
                    }
                }

                results.append(result)

            return results

        except Exception as e:
            logger.error(f"❌ 智能推理失败: {e}")
            return []

    def run_backtest(self, model_id: str, symbol: str, timeframe: str,
                    start_date: str, end_date: str, initial_balance: float = 10000,
                    lot_size: float = 0.01, stop_loss_pips: int = 50,
                    take_profit_pips: int = 100, min_confidence: float = 0.1,
                    cliff_brake_enabled: bool = False, trailing_stop_enabled: bool = False,
                    trailing_stop_distance: int = 20, trailing_stop_step: int = 10) -> Dict[str, Any]:
        """运行AI推理交易回测（支持悬崖勒马和移动止损功能）"""
        try:
            logger.info(f"🔄 开始AI推理交易回测: {model_id}")

            # 获取模型信息
            model = self.get_model_by_id(model_id)
            if not model:
                return {'success': False, 'error': '模型不存在'}

            # 使用模型的symbol和timeframe，如果参数为空的话
            actual_symbol = symbol if symbol and symbol.strip() else model.get('symbol', 'XAUUSD')
            actual_timeframe = timeframe if timeframe and timeframe.strip() else model.get('timeframe', 'H1')

            logger.info(f"📊 使用交易品种: {actual_symbol}, 时间框架: {actual_timeframe}")

            # 获取历史数据
            data_result = self._get_historical_data(actual_symbol, actual_timeframe, start_date, end_date, 1000)
            if not data_result.get('success'):
                return {'success': False, 'error': '获取历史数据失败'}

            historical_data = data_result['data']

            # 改进数据验证逻辑
            if not historical_data or len(historical_data) == 0:
                return {'success': False, 'error': '未获取到历史数据，请检查MT5连接和品种设置'}

            if len(historical_data) < 20:  # 降低最小数据要求
                logger.warning(f"⚠️ 历史数据较少({len(historical_data)}条)，但继续执行回测")

            # 验证数据质量
            valid_data = []
            for item in historical_data:
                if (item.get('close', 0) > 0 and
                    item.get('open', 0) > 0 and
                    item.get('high', 0) > 0 and
                    item.get('low', 0) > 0):
                    valid_data.append(item)

            if len(valid_data) < 10:
                return {'success': False, 'error': f'有效数据不足({len(valid_data)}条)，无法进行回测'}

            historical_data = valid_data

            logger.info(f"📊 获取历史数据: {len(historical_data)} 个数据点")

            # 执行回测
            backtest_result = self._execute_backtest(
                model, historical_data, initial_balance, lot_size,
                stop_loss_pips, take_profit_pips, min_confidence, cliff_brake_enabled,
                trailing_stop_enabled, trailing_stop_distance, trailing_stop_step
            )

            return backtest_result

        except Exception as e:
            logger.error(f"❌ AI推理交易回测失败: {e}")
            return {'success': False, 'error': str(e)}

    def _execute_backtest(self, model: Dict, historical_data: List[Dict],
                         initial_balance: float, lot_size: float,
                         stop_loss_pips: int, take_profit_pips: int,
                         min_confidence: float, cliff_brake_enabled: bool = False,
                         trailing_stop_enabled: bool = False, trailing_stop_distance: int = 20,
                         trailing_stop_step: int = 10) -> Dict[str, Any]:
        """执行回测逻辑（增加悬崖勒马功能）"""
        try:
            import numpy as np

            balance = initial_balance
            trades = []
            positions = []
            equity_curve = [balance]

            # 悬崖勒马功能相关变量
            recent_trades = []  # 记录最近的交易历史
            cliff_brake_active = False  # 悬崖勒马是否激活

            # 滑动窗口进行推理和交易
            window_size = 20

            for i in range(window_size, len(historical_data)):
                current_data = historical_data[i-window_size:i+1]
                current_bar = historical_data[i]

                # 计算市场波动性用于动态调整止盈止损
                recent_prices = [d['close'] for d in current_data[-10:]]
                volatility = np.std(recent_prices) / np.mean(recent_prices) if len(recent_prices) > 1 else 0.01

                # 动态调整止盈止损（基于波动性）
                dynamic_stop_loss = max(stop_loss_pips, int(volatility * 10000))  # 最小原设置
                dynamic_take_profit = max(take_profit_pips, int(volatility * 15000))  # 1.5倍止损

                # 执行推理 - 使用窗口数据而不是单个数据点
                inference_results = self._intelligent_inference(current_data, model, True)

                if not inference_results:
                    continue

                # 获取最新的预测结果（最后一个）
                prediction_result = inference_results[-1]
                prediction = prediction_result['prediction']
                confidence = prediction_result.get('confidence', 0)
                current_price = prediction_result['current_price']

                # 添加调试信息
                if i % 50 == 0:  # 每50个数据点输出一次调试信息
                    logger.info(f"📊 回测进度: {i}/{len(historical_data)}, 预测: {prediction}, 置信度: {confidence:.3f}, 价格: {current_price:.5f}")

                # 检查置信度阈值
                if confidence < min_confidence:
                    continue

                # 处理现有持仓
                for pos in positions[:]:  # 复制列表以避免修改时的问题
                    exit_price = current_price
                    profit_pips = 0
                    should_close = False

                    # 计算价格变动和盈亏
                    price_change = 0
                    if pos['type'] == 'BUY':
                        price_change = exit_price - pos['entry_price']
                    else:  # SELL
                        price_change = pos['entry_price'] - exit_price

                    # 转换为点数用于止盈止损判断
                    profit_pips = price_change * 100  # XAUUSD 1点 = 0.01

                    # 移动止损逻辑
                    if trailing_stop_enabled and 'trailing_stop' in pos:
                        # 检查是否需要更新移动止损位
                        if pos['type'] == 'BUY':
                            # 买单：价格上涨时移动止损位上移
                            if profit_pips > trailing_stop_distance:
                                new_trailing_stop = exit_price - (trailing_stop_step / 100)
                                if new_trailing_stop > pos['trailing_stop']:
                                    pos['trailing_stop'] = new_trailing_stop
                                    logger.debug(f"📈 买单移动止损更新: {pos['trailing_stop']:.5f}")

                            # 检查是否触发移动止损
                            if exit_price <= pos['trailing_stop']:
                                should_close = True
                                logger.info(f"🛑 买单触发移动止损: 价格{exit_price:.5f} <= 止损{pos['trailing_stop']:.5f}")

                        else:  # SELL
                            # 卖单：价格下跌时移动止损位下移
                            if profit_pips > trailing_stop_distance:
                                new_trailing_stop = exit_price + (trailing_stop_step / 100)
                                if new_trailing_stop < pos['trailing_stop']:
                                    pos['trailing_stop'] = new_trailing_stop
                                    logger.debug(f"📉 卖单移动止损更新: {pos['trailing_stop']:.5f}")

                            # 检查是否触发移动止损
                            if exit_price >= pos['trailing_stop']:
                                should_close = True
                                logger.info(f"🛑 卖单触发移动止损: 价格{exit_price:.5f} >= 止损{pos['trailing_stop']:.5f}")

                    # 检查是否触发动态止盈止损（如果没有被移动止损触发）
                    if not should_close and (profit_pips >= dynamic_take_profit or profit_pips <= -dynamic_stop_loss):
                        should_close = True

                    if should_close:
                        # 修正XAUUSD盈亏计算：0.01手每1美元价格变动 = $0.01盈亏
                        symbol = model.get('symbol', 'XAUUSD')
                        if symbol == 'XAUUSD':
                            # XAUUSD: 0.01手，每1美元价格变动 = $0.01盈亏
                            profit = price_change * lot_size * 100
                        else:
                            # 外汇：使用点数计算
                            profit = profit_pips * lot_size * 0.1

                        balance += profit

                        # 记录交易 - 添加手数信息
                        trades.append({
                            'timestamp': current_bar['timestamp'].isoformat() if hasattr(current_bar['timestamp'], 'isoformat') else str(current_bar['timestamp']),
                            'prediction': pos['type'],
                            'confidence': pos['confidence'],
                            'entry_price': pos['entry_price'],
                            'exit_price': exit_price,
                            'lot_size': lot_size,  # 添加手数
                            'profit': profit,
                            'balance': balance,
                            'pips': profit_pips
                        })

                        # 记录交易到最近交易历史
                        recent_trades.append({
                            'timestamp': current_bar['timestamp'],
                            'prediction': pos['type'],
                            'entry_price': pos['entry_price'],
                            'exit_price': exit_price,
                            'profit': profit,
                            'is_loss': profit < 0
                        })

                        # 只保留最近10笔交易
                        if len(recent_trades) > 10:
                            recent_trades.pop(0)

                        positions.remove(pos)
                        equity_curve.append(balance)

                # 开新仓位前进行悬崖勒马检测
                if prediction in ['BUY', 'SELL'] and len(positions) < 3:  # 最多3个持仓

                    # 悬崖勒马功能检测
                    if cliff_brake_enabled and len(recent_trades) >= 2:
                        cliff_brake_result = self._check_cliff_brake(recent_trades, current_price, prediction)

                        if cliff_brake_result['should_reverse']:
                            # 反转交易方向
                            original_prediction = prediction
                            prediction = 'SELL' if prediction == 'BUY' else 'BUY'

                            logger.info(f"🚨 悬崖勒马触发: {original_prediction} -> {prediction}")
                            logger.info(f"   原因: {cliff_brake_result['reason']}")
                            logger.info(f"   价格趋势: {cliff_brake_result['price_trend']}")

                            cliff_brake_active = True
                        elif cliff_brake_result['should_skip']:
                            # 跳过这次交易
                            logger.info(f"⏭️ 悬崖勒马跳过交易: {cliff_brake_result['reason']}")
                            continue

                    # 执行交易（可能已被悬崖勒马修改）
                    new_position = {
                        'type': prediction,
                        'entry_price': current_price,
                        'confidence': confidence,
                        'timestamp': current_bar['timestamp'],
                        'cliff_brake_applied': cliff_brake_active
                    }

                    # 初始化移动止损位
                    if trailing_stop_enabled:
                        if prediction == 'BUY':
                            # 买单：初始止损位在入场价下方
                            new_position['trailing_stop'] = current_price - (stop_loss_pips / 100)
                        else:  # SELL
                            # 卖单：初始止损位在入场价上方
                            new_position['trailing_stop'] = current_price + (stop_loss_pips / 100)

                        logger.debug(f"🎯 {prediction}单开仓: 价格{current_price:.5f}, 初始移动止损{new_position['trailing_stop']:.5f}")

                    positions.append(new_position)

                    cliff_brake_active = False  # 重置标志

            # 计算统计数据
            statistics = self._calculate_backtest_statistics(trades, initial_balance, balance, equity_curve)

            logger.info(f"✅ 回测完成: {len(trades)} 笔交易, 总收益: {statistics['total_return']:.2f}%")

            return {
                'success': True,
                'statistics': statistics,
                'trades': trades,
                'equity_curve': equity_curve,
                'initial_balance': initial_balance,
                'final_balance': balance
            }

        except Exception as e:
            logger.error(f"❌ 执行回测失败: {e}")
            return {'success': False, 'error': str(e)}

    def _check_cliff_brake(self, recent_trades: List[Dict], current_price: float,
                          predicted_action: str) -> Dict[str, Any]:
        """
        悬崖勒马检测逻辑

        Args:
            recent_trades: 最近的交易历史
            current_price: 当前价格
            predicted_action: AI预测的交易方向

        Returns:
            Dict: 包含是否应该反转、跳过交易等信息
        """
        try:
            # 检查最近2笔交易是否都是亏损
            if len(recent_trades) < 2:
                return {'should_reverse': False, 'should_skip': False, 'reason': '交易历史不足'}

            last_two_trades = recent_trades[-2:]

            # 检查前2笔交易是否都是亏损
            if not (last_two_trades[0]['is_loss'] and last_two_trades[1]['is_loss']):
                return {'should_reverse': False, 'should_skip': False, 'reason': '前2笔交易未全部亏损'}

            # 获取价格序列：第1单价格 -> 第2单价格 -> 当前价格
            price1 = last_two_trades[0]['entry_price']  # 第1单价格
            price2 = last_two_trades[1]['entry_price']  # 第2单价格
            price3 = current_price                      # 当前价格

            # 获取前2单的交易方向
            action1 = last_two_trades[0]['prediction']  # 第1单方向
            action2 = last_two_trades[1]['prediction']  # 第2单方向

            # 检查前2单是否同向
            if action1 != action2:
                return {'should_reverse': False, 'should_skip': False,
                       'reason': '前2单方向不一致，无法判断趋势'}

            # 分析价格趋势和交易方向的匹配度
            price_trend = self._analyze_price_trend(price1, price2, price3)

            # 悬崖勒马核心逻辑
            should_reverse = False
            reason = ""

            if action1 == 'BUY' and action2 == 'BUY':
                # 前2单都是买涨但都亏损
                if price_trend == 'downward' and predicted_action == 'BUY':
                    # 价格呈下跌趋势，但AI还要买涨 -> 反转为卖跌
                    should_reverse = True
                    reason = f"前2单买涨亏损，价格趋势下跌({price1:.5f}>{price2:.5f}>{price3:.5f})，反转为卖跌"

            elif action1 == 'SELL' and action2 == 'SELL':
                # 前2单都是卖跌但都亏损
                if price_trend == 'upward' and predicted_action == 'SELL':
                    # 价格呈上涨趋势，但AI还要卖跌 -> 反转为买涨
                    should_reverse = True
                    reason = f"前2单卖跌亏损，价格趋势上涨({price1:.5f}<{price2:.5f}<{price3:.5f})，反转为买涨"

            return {
                'should_reverse': should_reverse,
                'should_skip': False,
                'reason': reason,
                'price_trend': price_trend,
                'price_sequence': f"{price1:.5f} -> {price2:.5f} -> {price3:.5f}",
                'action_sequence': f"{action1} -> {action2} -> {predicted_action}"
            }

        except Exception as e:
            logger.error(f"悬崖勒马检测失败: {e}")
            return {'should_reverse': False, 'should_skip': False, 'reason': f'检测异常: {str(e)}'}

    def _analyze_price_trend(self, price1: float, price2: float, price3: float) -> str:
        """
        分析价格趋势

        Args:
            price1: 第1个价格点
            price2: 第2个价格点
            price3: 第3个价格点（当前价格）

        Returns:
            str: 'upward'(上涨), 'downward'(下跌), 'sideways'(横盘)
        """
        try:
            # 计算价格变化的阈值（0.01%）
            threshold = min(price1, price2, price3) * 0.0001

            # 判断整体趋势
            if price1 > price2 + threshold and price2 > price3 + threshold:
                return 'downward'  # 明显下跌趋势
            elif price1 + threshold < price2 and price2 + threshold < price3:
                return 'upward'    # 明显上涨趋势
            else:
                return 'sideways'  # 横盘或不明确趋势

        except Exception as e:
            logger.error(f"价格趋势分析失败: {e}")
            return 'sideways'

    def _get_recent_ai_trades(self, user_id: int, symbol: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取最近的AI交易历史（用于悬崖勒马检测）

        Args:
            user_id: 用户ID
            symbol: 交易品种
            limit: 获取数量限制

        Returns:
            List[Dict]: 最近的交易历史
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 查询最近的AI交易记录（已平仓的交易）
            cursor.execute('''
                SELECT
                    created_at as timestamp,
                    action as prediction,
                    entry_price,
                    COALESCE(close_price, entry_price) as exit_price,
                    profit,
                    CASE WHEN profit < 0 THEN 1 ELSE 0 END as is_loss
                FROM ai_trades
                WHERE user_id = ? AND symbol = ? AND status = 'closed'
                ORDER BY created_at DESC
                LIMIT ?
            ''', (user_id, symbol, limit))

            trades = cursor.fetchall()
            conn.close()

            # 转换为字典格式
            recent_trades = []
            for trade in trades:
                recent_trades.append({
                    'timestamp': trade[0],
                    'prediction': trade[1],
                    'entry_price': trade[2] or 0,
                    'exit_price': trade[3] or 0,
                    'profit': trade[4] or 0,
                    'is_loss': bool(trade[5])
                })

            # 按时间正序排列（最早的在前面）
            recent_trades.reverse()

            logger.debug(f"获取最近AI交易历史: {len(recent_trades)} 笔")
            return recent_trades

        except Exception as e:
            logger.error(f"获取AI交易历史失败: {e}")
            return []

    def run_parameter_optimization(self, model_id: str, symbol: str, timeframe: str,
                                 optimization_period: str = 'week',
                                 risk_preference: str = 'balanced') -> Dict[str, Any]:
        """
        运行参数评测自动优选回测功能

        Args:
            model_id: 模型ID
            symbol: 交易品种
            timeframe: 时间框架
            optimization_period: 优化周期 ('week' 或 'month')
            risk_preference: 风险偏好 ('high_return_high_risk', 'medium_return_low_risk', 'low_return_ultra_low_risk', 'balanced')

        Returns:
            Dict: 优化结果，包含最佳参数组合和排名
        """
        try:
            logger.info(f"🔍 开始参数优化: {symbol} {timeframe} ({optimization_period})")

            # 检查MT5连接状态
            try:
                from services.mt5_service import mt5_service
                connection_status = mt5_service.get_connection_status()
                if not connection_status.get('connected', False):
                    logger.warning("⚠️ MT5未连接，参数优化可能会失败")
                    return {
                        'success': False,
                        'error': 'MT5未连接，无法获取历史数据进行参数优化。请确保MT5正常连接后重试。'
                    }
                else:
                    logger.info("✅ MT5连接正常，开始参数优化")
            except Exception as mt5_check_error:
                logger.error(f"❌ MT5连接检查失败: {mt5_check_error}")
                return {
                    'success': False,
                    'error': f'MT5连接检查失败: {mt5_check_error}'
                }

            # 设置回测时间范围
            end_date = datetime.now()
            if optimization_period == 'week':
                start_date = end_date - timedelta(days=7)
                logger.info("📅 使用一周数据进行参数优化")
            else:  # month
                start_date = end_date - timedelta(days=30)
                logger.info("📅 使用一个月数据进行参数优化")

            # 定义参数组合
            parameter_combinations = self._generate_parameter_combinations()
            logger.info(f"🧪 生成 {len(parameter_combinations)} 个参数组合")

            # 存储所有回测结果
            optimization_results = []

            # 🚀 优化：一次获取历史数据，多次回测比较
            logger.info("📊 获取历史数据用于参数优化...")

            # 获取模型信息
            model = self.get_model_by_id(model_id)
            if not model:
                return {'success': False, 'error': '模型不存在'}

            # 使用模型的symbol和timeframe，如果参数为空的话
            actual_symbol = symbol if symbol and symbol.strip() else model.get('symbol', 'XAUUSD')
            actual_timeframe = timeframe if timeframe and timeframe.strip() else model.get('timeframe', 'H1')

            # 一次性获取历史数据
            data_result = self._get_historical_data(actual_symbol, actual_timeframe,
                                                  start_date.strftime('%Y-%m-%d'),
                                                  end_date.strftime('%Y-%m-%d'), 1000)
            if not data_result.get('success'):
                return {'success': False, 'error': '获取历史数据失败'}

            historical_data = data_result['data']

            # 验证数据质量
            if not historical_data or len(historical_data) == 0:
                return {'success': False, 'error': '未获取到历史数据，请检查MT5连接和品种设置'}

            if len(historical_data) < 20:
                logger.warning(f"⚠️ 历史数据较少({len(historical_data)}条)，但继续执行回测")

            # 验证数据质量
            valid_data = []
            for item in historical_data:
                if (item.get('close', 0) > 0 and item.get('open', 0) > 0 and
                    item.get('high', 0) > 0 and item.get('low', 0) > 0):
                    valid_data.append(item)

            if len(valid_data) < 10:
                return {'success': False, 'error': f'有效数据不足({len(valid_data)}条)，无法进行回测'}

            historical_data = valid_data
            logger.info(f"✅ 成功获取 {len(historical_data)} 个MT5历史数据点")

            # 逐个测试参数组合（使用相同的历史数据）
            for i, params in enumerate(parameter_combinations):
                try:
                    logger.info(f"🔄 测试参数组合 {i+1}/{len(parameter_combinations)}: {params}")

                    # 执行回测（使用已获取的历史数据）
                    backtest_result = self._execute_backtest(
                        model, historical_data,
                        params['initial_balance'],
                        params['lot_size'],
                        params['stop_loss_pips'],
                        params['take_profit_pips'],
                        params['min_confidence'],
                        params.get('cliff_brake_enabled', False),
                        params.get('trailing_stop_enabled', False),
                        params.get('trailing_stop_distance', 20),
                        params.get('trailing_stop_step', 10)
                    )

                    if backtest_result.get('success'):
                        # 计算所有排序方式的得分
                        all_scores = self._calculate_all_scores(backtest_result, params)
                        # 使用当前风险偏好的得分作为主要得分
                        score = all_scores.get(risk_preference, 0)

                        optimization_results.append({
                            'rank': 0,  # 稍后排序时设置
                            'parameters': params,
                            'backtest_result': backtest_result,
                            'score': score,
                            'all_scores': all_scores,  # 保存所有排序方式的得分
                            'total_return': backtest_result.get('statistics', {}).get('total_return', 0),
                            'win_rate': backtest_result.get('statistics', {}).get('win_rate', 0),
                            'max_drawdown': backtest_result.get('statistics', {}).get('max_drawdown', 0),
                            'sharpe_ratio': backtest_result.get('statistics', {}).get('sharpe_ratio', 0),
                            'total_trades': backtest_result.get('statistics', {}).get('total_trades', 0),
                            'winning_trades': backtest_result.get('statistics', {}).get('winning_trades', 0),
                            'losing_trades': backtest_result.get('statistics', {}).get('losing_trades', 0)
                        })

                        logger.info(f"✅ 参数组合 {i+1} 完成: 收益率={backtest_result.get('statistics', {}).get('total_return', 0):.2f}%, 评分={score:.2f}")
                    else:
                        error_msg = backtest_result.get('error', '未知错误')
                        logger.warning(f"⚠️ 参数组合 {i+1} 回测失败: {error_msg}")

                        # 如果是数据获取失败，可能是MT5连接问题，尝试等待后继续
                        if '历史数据' in error_msg or 'MT5' in error_msg:
                            logger.info("💤 等待2秒后继续...")
                            time.sleep(2)

                except Exception as e:
                    logger.error(f"❌ 参数组合 {i+1} 测试异常: {e}")
                    continue

            # 按评分排序
            optimization_results.sort(key=lambda x: x['score'], reverse=True)

            # 设置排名
            for i, result in enumerate(optimization_results):
                result['rank'] = i + 1

            # 生成优化报告
            optimization_report = self._generate_optimization_report(
                optimization_results, symbol, timeframe, optimization_period
            )

            logger.info(f"🎉 参数优化完成，共测试 {len(parameter_combinations)} 个组合，成功 {len(optimization_results)} 个")

            # 保存优化结果到数据库
            optimization_result_data = {
                'success': True,
                'optimization_results': optimization_results[:20],  # 返回前20名
                'best_parameters': optimization_results[0]['parameters'] if optimization_results else None,
                'optimization_report': optimization_report,
                'total_combinations': len(parameter_combinations),
                'successful_combinations': len(optimization_results),
                'optimization_period': optimization_period,
                'risk_preference': risk_preference,
                'date_range': {
                    'start_date': start_date.strftime('%Y-%m-%d'),
                    'end_date': end_date.strftime('%Y-%m-%d')
                }
            }

            # 保存到数据库
            self._save_optimization_results(
                model_id, symbol, timeframe, optimization_period,
                risk_preference, optimization_result_data
            )

            return optimization_result_data

        except Exception as e:
            logger.error(f"❌ 参数优化失败: {e}")
            return {'success': False, 'error': str(e)}

    def resort_optimization_results(self, model_id: str, symbol: str, timeframe: str,
                                  optimization_period: str, new_risk_preference: str) -> Dict[str, Any]:
        """重新排序已有的参数优化结果，避免重复执行回测"""
        try:
            logger.info(f"🔄 重新排序参数优化结果: {new_risk_preference}")

            # 从数据库获取最近的优化结果
            saved_results = self.get_saved_optimization_results(
                model_id, symbol, timeframe, None  # 不限制风险偏好，获取最新结果
            )

            if not saved_results.get('success') or not saved_results.get('optimization_results'):
                logger.warning("⚠️ 没有找到可重新排序的优化结果")
                return {'success': False, 'error': '没有找到可重新排序的优化结果，请先执行参数优化'}

            # 获取原始结果
            original_results = saved_results['optimization_results']
            logger.info(f"📊 找到 {len(original_results)} 个参数组合结果")

            # 重新排序
            resorting_results = []
            for result in original_results:
                # 检查是否有所有得分数据
                if 'all_scores' in result and new_risk_preference in result['all_scores']:
                    # 使用已计算的得分
                    new_score = result['all_scores'][new_risk_preference]
                    result_copy = result.copy()
                    result_copy['score'] = new_score
                    resorting_results.append(result_copy)
                else:
                    # 如果没有预计算的得分，重新计算
                    logger.warning(f"⚠️ 参数组合缺少 {new_risk_preference} 得分，重新计算")
                    if 'backtest_result' in result:
                        new_score = self._calculate_optimization_score(
                            result['backtest_result'], result['parameters'], new_risk_preference
                        )
                        result_copy = result.copy()
                        result_copy['score'] = new_score
                        resorting_results.append(result_copy)

            # 按新得分排序
            resorting_results.sort(key=lambda x: x['score'], reverse=True)

            # 重新设置排名
            for i, result in enumerate(resorting_results):
                result['rank'] = i + 1

            # 生成新的优化报告
            optimization_report = self._generate_optimization_report(
                resorting_results, symbol, timeframe, optimization_period
            )

            # 构建返回结果
            resort_result = {
                'success': True,
                'optimization_results': resorting_results[:20],  # 返回前20名
                'best_parameters': resorting_results[0]['parameters'] if resorting_results else None,
                'optimization_report': optimization_report,
                'total_combinations': len(original_results),
                'successful_combinations': len(resorting_results),
                'optimization_period': optimization_period,
                'risk_preference': new_risk_preference,
                'date_range': saved_results.get('date_range', {}),
                'is_resorted': True  # 标记这是重新排序的结果
            }

            # 保存重新排序的结果
            self._save_optimization_results(
                model_id, symbol, timeframe, optimization_period,
                new_risk_preference, resort_result
            )

            logger.info(f"✅ 重新排序完成，最佳参数得分: {resorting_results[0]['score']:.2f}")
            return resort_result

        except Exception as e:
            logger.error(f"❌ 重新排序失败: {e}")
            return {'success': False, 'error': str(e)}

    def export_optimization_results_csv(self, model_id: str, symbol: str, timeframe: str,
                                      optimization_period: str, risk_preference: str) -> Dict[str, Any]:
        """导出参数优化结果为CSV格式"""
        try:
            import csv
            import io
            from datetime import datetime

            logger.info(f"📊 开始导出参数优化结果: {symbol} {timeframe} ({risk_preference})")

            # 获取保存的优化结果
            saved_results = self.get_saved_optimization_results(
                model_id, symbol, timeframe, risk_preference
            )

            if not saved_results.get('success') or not saved_results.get('optimization_results'):
                return {'success': False, 'error': '没有找到可导出的参数优化结果'}

            optimization_results = saved_results['optimization_results']

            # 创建CSV内容
            output = io.StringIO()
            writer = csv.writer(output)

            # 写入标题行
            headers = [
                '排名', '评分', '收益率(%)', '胜率(%)', '盈利单数', '亏损单数', '总交易数',
                '手数', '止损(pips)', '止盈(pips)', '置信度(%)', '悬崖勒马', '移动止损',
                '止损距离(pips)', '止损步长(pips)', '最大回撤(%)', '夏普比率',
                '总盈利($)', '总亏损($)', '净盈利($)', '盈利因子', '平均盈利($)', '平均亏损($)',
                '最大单笔盈利($)', '最大单笔亏损($)', '最大连续盈利', '最大连续亏损'
            ]
            writer.writerow(headers)

            # 写入数据行
            for result in optimization_results:
                params = result.get('parameters', {})
                stats = result.get('backtest_result', {}).get('statistics', {})

                row = [
                    result.get('rank', ''),
                    result.get('score', ''),
                    f"{result.get('total_return', 0):.2f}",
                    f"{result.get('win_rate', 0):.1f}",
                    result.get('winning_trades', 0),
                    result.get('losing_trades', 0),
                    result.get('total_trades', 0),
                    params.get('lot_size', ''),
                    params.get('stop_loss_pips', ''),
                    params.get('take_profit_pips', ''),
                    f"{(params.get('min_confidence', 0) * 100):.0f}",
                    '是' if params.get('cliff_brake_enabled', False) else '否',
                    '是' if params.get('trailing_stop_enabled', False) else '否',
                    params.get('trailing_stop_distance', ''),
                    params.get('trailing_stop_step', ''),
                    f"{abs(result.get('max_drawdown', 0)):.2f}",
                    f"{result.get('sharpe_ratio', 0):.2f}",
                    f"{stats.get('gross_profit', 0):.2f}",
                    f"{stats.get('gross_loss', 0):.2f}",
                    f"{stats.get('net_profit', 0):.2f}",
                    f"{stats.get('profit_factor', 0):.2f}",
                    f"{stats.get('average_win', 0):.2f}",
                    f"{stats.get('average_loss', 0):.2f}",
                    f"{stats.get('max_win', 0):.2f}",
                    f"{abs(stats.get('max_loss', 0)):.2f}",
                    stats.get('max_consecutive_wins', 0),
                    stats.get('max_consecutive_losses', 0)
                ]
                writer.writerow(row)

            # 添加摘要信息
            writer.writerow([])  # 空行
            writer.writerow(['=== 优化摘要 ==='])
            writer.writerow(['模型ID', model_id])
            writer.writerow(['交易品种', symbol])
            writer.writerow(['时间框架', timeframe])
            writer.writerow(['优化周期', '一周' if optimization_period == 'week' else '一个月'])
            writer.writerow(['风险偏好', self._get_risk_preference_name(risk_preference)])
            writer.writerow(['总参数组合数', len(optimization_results)])
            writer.writerow(['导出时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S')])

            if saved_results.get('date_range'):
                date_range = saved_results['date_range']
                writer.writerow(['数据开始日期', date_range.get('start_date', '')])
                writer.writerow(['数据结束日期', date_range.get('end_date', '')])

            csv_content = output.getvalue()
            output.close()

            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"参数优化结果_{symbol}_{timeframe}_{risk_preference}_{timestamp}.csv"

            logger.info(f"✅ 参数优化结果导出完成: {len(optimization_results)} 条记录")

            return {
                'success': True,
                'csv_content': csv_content,
                'filename': filename,
                'record_count': len(optimization_results)
            }

        except Exception as e:
            logger.error(f"❌ 导出参数优化结果失败: {e}")
            return {'success': False, 'error': str(e)}

    def _get_risk_preference_name(self, preference: str) -> str:
        """获取风险偏好的中文名称"""
        names = {
            'balanced': '平衡模式',
            'high_return_high_risk': '高收益高风险',
            'medium_return_low_risk': '中等收益低风险',
            'low_return_ultra_low_risk': '低收益超低风险'
        }
        return names.get(preference, preference)

    def _save_optimization_results(self, model_id: str, symbol: str, timeframe: str,
                                 optimization_period: str, risk_preference: str,
                                 optimization_data: Dict[str, Any]):
        """保存参数优化结果到数据库"""
        try:
            import uuid
            import json
            from datetime import datetime

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            optimization_id = str(uuid.uuid4())
            current_time = datetime.now().isoformat()

            # 获取当前用户ID（这里简化处理，实际应该从session获取）
            user_id = 1  # 默认用户ID，实际应用中需要从登录状态获取

            cursor.execute('''
                INSERT INTO parameter_optimization_results
                (id, user_id, model_id, symbol, timeframe, optimization_period,
                 risk_preference, optimization_results, best_parameters,
                 total_combinations, successful_combinations, date_range, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                optimization_id,
                user_id,
                model_id,
                symbol,
                timeframe,
                optimization_period,
                risk_preference,
                json.dumps(optimization_data['optimization_results']),
                json.dumps(optimization_data['best_parameters']),
                optimization_data['total_combinations'],
                optimization_data['successful_combinations'],
                json.dumps(optimization_data['date_range']),
                current_time
            ))

            conn.commit()
            conn.close()

            logger.info(f"✅ 参数优化结果已保存到数据库: {optimization_id}")

        except Exception as e:
            logger.error(f"❌ 保存参数优化结果失败: {e}")

    def get_saved_optimization_results(self, model_id: str, symbol: str = None,
                                     timeframe: str = None, risk_preference: str = None) -> Dict[str, Any]:
        """获取保存的参数优化结果"""
        try:
            import json

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 构建查询条件
            where_conditions = ['model_id = ?']
            params = [model_id]

            if symbol:
                where_conditions.append('symbol = ?')
                params.append(symbol)

            if timeframe:
                where_conditions.append('timeframe = ?')
                params.append(timeframe)

            if risk_preference:
                where_conditions.append('risk_preference = ?')
                params.append(risk_preference)

            where_clause = ' AND '.join(where_conditions)

            cursor.execute(f'''
                SELECT id, symbol, timeframe, optimization_period, risk_preference,
                       optimization_results, best_parameters, total_combinations,
                       successful_combinations, date_range, created_at
                FROM parameter_optimization_results
                WHERE {where_clause}
                ORDER BY created_at DESC
                LIMIT 1
            ''', params)

            result = cursor.fetchone()
            conn.close()

            if result:
                return {
                    'success': True,
                    'optimization_id': result[0],
                    'symbol': result[1],
                    'timeframe': result[2],
                    'optimization_period': result[3],
                    'risk_preference': result[4],
                    'optimization_results': json.loads(result[5]),
                    'best_parameters': json.loads(result[6]),
                    'total_combinations': result[7],
                    'successful_combinations': result[8],
                    'date_range': json.loads(result[9]),
                    'created_at': result[10]
                }
            else:
                return {'success': False, 'error': '未找到保存的优化结果'}

        except Exception as e:
            logger.error(f"❌ 获取保存的优化结果失败: {e}")
            return {'success': False, 'error': str(e)}

    def _generate_parameter_combinations(self) -> List[Dict[str, Any]]:
        """生成参数组合"""
        try:
            # 定义参数范围（完整参数优化，包含所有交易参数）
            parameter_ranges = {
                'initial_balance': [10000],  # 固定初始资金
                'lot_size': [0.01],  # 手数：固定为0.01
                'stop_loss_pips': [50, 70, 80],  # 止损点数：3个值
                'take_profit_pips': [60, 80, 100, 150],  # 止盈点数：4个值
                'min_confidence': [0.30, 0.40, 0.50, 0.60, 0.70, 0.80],  # 最小置信度：6个值
                'cliff_brake_enabled': [False, True],  # 悬崖勒马开关：2个值
                'trailing_stop_enabled': [False, True],  # 移动止损开关：2个值
                'trailing_stop_distance': [15, 20, 25, 30],  # 移动止损触发距离：4个值
                'trailing_stop_step': [5, 10, 15, 20]  # 移动止损跟踪步长：4个值
            }

            # 生成所有组合
            import itertools

            combinations = []
            keys = list(parameter_ranges.keys())
            values = list(parameter_ranges.values())

            for combination in itertools.product(*values):
                params = dict(zip(keys, combination))

                # 添加合理性检查
                if self._is_valid_parameter_combination(params):
                    combinations.append(params)

            logger.info(f"📊 生成参数组合: {len(combinations)} 个有效组合")
            return combinations

        except Exception as e:
            logger.error(f"❌ 生成参数组合失败: {e}")
            return []

    def _is_valid_parameter_combination(self, params: Dict[str, Any]) -> bool:
        """检查参数组合的合理性"""
        try:
            # 风险回报比检查：止盈应该大于止损
            if params['take_profit_pips'] <= params['stop_loss_pips']:
                return False

            # 风险回报比不应该过于极端
            risk_reward_ratio = params['take_profit_pips'] / params['stop_loss_pips']
            if risk_reward_ratio < 1.2 or risk_reward_ratio > 10:
                return False

            # 手数和置信度的合理搭配
            if params['lot_size'] >= 0.05 and params['min_confidence'] < 0.5:
                return False  # 大手数需要高置信度

            # 移动止损参数合理性检查
            if params.get('trailing_stop_enabled', False):
                trailing_distance = params.get('trailing_stop_distance', 20)
                trailing_step = params.get('trailing_stop_step', 10)

                # 触发距离必须大于跟踪步长
                if trailing_distance <= trailing_step:
                    return False

                # 移动止损距离不应该太大，避免过于保守
                if trailing_distance > params['stop_loss_pips']:
                    return False

            return True

        except Exception as e:
            logger.error(f"参数组合验证失败: {e}")
            return False

    def _calculate_optimization_score(self, backtest_result: Dict[str, Any],
                                    params: Dict[str, Any], risk_preference: str = 'balanced') -> float:
        """计算优化评分（支持用户风险偏好选择）"""
        try:
            stats = backtest_result.get('statistics', {})

            # 获取关键指标
            total_return = stats.get('total_return', 0)
            win_rate = stats.get('win_rate', 0)
            max_drawdown = abs(stats.get('max_drawdown', 0))
            sharpe_ratio = stats.get('sharpe_ratio', 0)
            total_trades = stats.get('total_trades', 0)

            # 根据用户风险偏好设置权重
            if risk_preference == 'high_return_high_risk':
                # 高收益高风险：极度重视收益率，容忍高风险
                return_weight = 85
                win_rate_weight = 5
                drawdown_weight = 5
                sharpe_weight = 3
                trade_weight = 2
                return_multiplier = 6  # 14%收益率得满分
                drawdown_penalty = 10  # 减少回撤惩罚
            elif risk_preference == 'medium_return_low_risk':
                # 中等收益低风险：平衡收益和风险控制
                return_weight = 50
                win_rate_weight = 20
                drawdown_weight = 20
                sharpe_weight = 7
                trade_weight = 3
                return_multiplier = 4  # 12.5%收益率得满分
                drawdown_penalty = 25  # 适中回撤惩罚
            elif risk_preference == 'low_return_ultra_low_risk':
                # 低收益超低风险：重视风险控制和稳定性
                return_weight = 30
                win_rate_weight = 30
                drawdown_weight = 25
                sharpe_weight = 10
                trade_weight = 5
                return_multiplier = 3  # 10%收益率得满分
                drawdown_penalty = 40  # 严格回撤惩罚
            else:
                # 默认平衡模式（原高收益优先模式）
                return_weight = 75
                win_rate_weight = 10
                drawdown_weight = 10
                sharpe_weight = 3
                trade_weight = 2
                return_multiplier = 5  # 15%收益率得满分
                drawdown_penalty = 20

            # 计算综合评分（0-100分）
            score = 0

            # 收益率得分
            return_score = min(total_return * return_multiplier, return_weight)
            score += return_score

            # 胜率得分
            win_rate_score = (win_rate / 100) * win_rate_weight
            score += win_rate_score

            # 最大回撤得分
            if max_drawdown > 0:
                drawdown_score = max(0, drawdown_weight - max_drawdown * drawdown_penalty)
            else:
                drawdown_score = drawdown_weight
            score += drawdown_score

            # 夏普比率得分
            sharpe_score = min(max(sharpe_ratio, 0) * (sharpe_weight / 2), sharpe_weight)
            score += sharpe_score

            # 交易次数得分
            if total_trades >= 1 and total_trades <= 50:
                trade_score = trade_weight
            elif total_trades > 0:
                trade_score = max(0, trade_weight - abs(total_trades - 20) * 0.05)
            else:
                trade_score = 0
            score += trade_score

            # 记录详细评分信息用于调试
            logger.debug(f"📊 评分详情({risk_preference}): 收益率={return_score:.1f}/{return_weight}, "
                        f"胜率={win_rate_score:.1f}/{win_rate_weight}, 回撤={drawdown_score:.1f}/{drawdown_weight}, "
                        f"夏普={sharpe_score:.1f}/{sharpe_weight}, 交易={trade_score:.1f}/{trade_weight}, "
                        f"总分={score:.1f}")

            return round(score, 2)

        except Exception as e:
            logger.error(f"计算优化评分失败: {e}")
            return 0

    def _calculate_all_scores(self, backtest_result: Dict[str, Any], params: Dict[str, Any]) -> Dict[str, float]:
        """计算所有排序方式的得分"""
        try:
            all_preferences = ['balanced', 'high_return_high_risk', 'medium_return_low_risk', 'low_return_ultra_low_risk']
            scores = {}

            for preference in all_preferences:
                scores[preference] = self._calculate_optimization_score(backtest_result, params, preference)

            return scores
        except Exception as e:
            logger.error(f"计算所有得分失败: {e}")
            return {}

    def _generate_optimization_report(self, results: List[Dict[str, Any]],
                                    symbol: str, timeframe: str, period: str) -> Dict[str, Any]:
        """生成优化报告"""
        try:
            if not results:
                return {'error': '没有有效的优化结果'}

            # 最佳结果
            best_result = results[0]

            # 统计信息
            total_results = len(results)
            avg_return = sum(r['total_return'] for r in results) / total_results
            avg_win_rate = sum(r['win_rate'] for r in results) / total_results
            avg_drawdown = sum(abs(r['max_drawdown']) for r in results) / total_results

            # 参数分析
            parameter_analysis = self._analyze_parameter_impact(results)

            # 生成报告
            report = {
                'summary': {
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'optimization_period': period,
                    'total_combinations_tested': total_results,
                    'best_score': best_result['score'],
                    'best_return': best_result['total_return'],
                    'best_win_rate': best_result['win_rate'],
                    'average_return': round(avg_return, 2),
                    'average_win_rate': round(avg_win_rate, 2),
                    'average_drawdown': round(avg_drawdown, 2)
                },
                'best_parameters': best_result['parameters'],
                'top_5_results': results[:5],
                'parameter_analysis': parameter_analysis,
                'recommendations': self._generate_parameter_recommendations(results)
            }

            return report

        except Exception as e:
            logger.error(f"生成优化报告失败: {e}")
            return {'error': str(e)}

    def _analyze_parameter_impact(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析参数对结果的影响"""
        try:
            analysis = {}

            # 分析每个参数的影响
            parameters = ['lot_size', 'stop_loss_pips', 'take_profit_pips', 'min_confidence', 'cliff_brake_enabled']

            for param in parameters:
                param_impact = {}
                param_values = {}

                # 按参数值分组
                for result in results:
                    param_value = result['parameters'][param]
                    if param_value not in param_values:
                        param_values[param_value] = []
                    param_values[param_value].append(result['score'])

                # 计算每个参数值的平均得分
                for value, scores in param_values.items():
                    param_impact[str(value)] = {
                        'average_score': round(sum(scores) / len(scores), 2),
                        'count': len(scores),
                        'best_score': max(scores),
                        'worst_score': min(scores)
                    }

                analysis[param] = param_impact

            return analysis

        except Exception as e:
            logger.error(f"参数影响分析失败: {e}")
            return {}

    def _generate_parameter_recommendations(self, results: List[Dict[str, Any]]) -> List[str]:
        """生成参数建议"""
        try:
            recommendations = []

            if not results:
                return recommendations

            # 分析最佳结果的特征
            top_results = results[:5]

            # 分析止损止盈比例
            risk_reward_ratios = []
            for result in top_results:
                params = result['parameters']
                ratio = params['take_profit_pips'] / params['stop_loss_pips']
                risk_reward_ratios.append(ratio)

            avg_rr_ratio = sum(risk_reward_ratios) / len(risk_reward_ratios)
            recommendations.append(f"建议风险回报比保持在 {avg_rr_ratio:.1f}:1 左右")

            # 分析置信度
            confidences = [r['parameters']['min_confidence'] for r in top_results]
            avg_confidence = sum(confidences) / len(confidences)
            recommendations.append(f"建议最小置信度设置为 {avg_confidence:.1f}")

            # 分析悬崖勒马效果
            cliff_brake_results = [r for r in top_results if r['parameters']['cliff_brake_enabled']]
            if len(cliff_brake_results) >= len(top_results) * 0.6:
                recommendations.append("建议启用悬崖勒马功能，可以提高整体表现")

            # 分析手数
            lot_sizes = [r['parameters']['lot_size'] for r in top_results]
            avg_lot_size = sum(lot_sizes) / len(lot_sizes)
            recommendations.append(f"建议手数设置为 {avg_lot_size:.2f}")

            return recommendations

        except Exception as e:
            logger.error(f"生成参数建议失败: {e}")
            return []

    def _calculate_backtest_statistics(self, trades: List[Dict], initial_balance: float,
                                     final_balance: float, equity_curve: List[float]) -> Dict[str, Any]:
        """计算回测统计数据"""
        try:
            if not trades:
                return {
                    'total_return': 0,
                    'win_rate': 0,
                    'total_trades': 0,
                    'winning_trades': 0,
                    'losing_trades': 0,
                    'max_drawdown': 0,
                    'sharpe_ratio': 0,
                    'profit_factor': 0
                }

            # 基本统计
            total_trades = len(trades)
            winning_trades = len([t for t in trades if t['profit'] > 0])
            losing_trades = len([t for t in trades if t['profit'] < 0])

            # 收益率
            total_return = ((final_balance - initial_balance) / initial_balance) * 100

            # 胜率
            win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0

            # 最大回撤
            max_drawdown = 0
            peak = initial_balance
            for balance in equity_curve:
                if balance > peak:
                    peak = balance
                drawdown = ((peak - balance) / peak) * 100
                if drawdown > max_drawdown:
                    max_drawdown = drawdown

            # 盈利因子和详细统计
            gross_profit = sum([t['profit'] for t in trades if t['profit'] > 0])
            gross_loss = abs(sum([t['profit'] for t in trades if t['profit'] < 0]))
            net_profit = gross_profit - gross_loss  # 净盈利

            # 修复无穷大值问题
            if gross_loss > 0:
                profit_factor = gross_profit / gross_loss
            elif gross_profit > 0:
                profit_factor = 999.99  # 使用大数值代替无穷大
            else:
                profit_factor = 0

            # 平仓交易统计
            break_even_trades = len([t for t in trades if t['profit'] == 0])

            # 最大连续盈利和亏损
            max_consecutive_wins = 0
            max_consecutive_losses = 0
            current_wins = 0
            current_losses = 0

            for trade in trades:
                if trade['profit'] > 0:
                    current_wins += 1
                    current_losses = 0
                    max_consecutive_wins = max(max_consecutive_wins, current_wins)
                elif trade['profit'] < 0:
                    current_losses += 1
                    current_wins = 0
                    max_consecutive_losses = max(max_consecutive_losses, current_losses)
                else:
                    current_wins = 0
                    current_losses = 0

            # 最大单笔盈利和亏损
            max_win = max([t['profit'] for t in trades if t['profit'] > 0]) if winning_trades > 0 else 0
            max_loss = min([t['profit'] for t in trades if t['profit'] < 0]) if losing_trades > 0 else 0

            # 平均持仓时间（如果有时间信息）
            avg_trade_duration = 0
            if trades and 'entry_time' in trades[0] and 'exit_time' in trades[0]:
                durations = []
                for trade in trades:
                    try:
                        entry_time = pd.to_datetime(trade['entry_time'])
                        exit_time = pd.to_datetime(trade['exit_time'])
                        duration = (exit_time - entry_time).total_seconds() / 60  # 分钟
                        durations.append(duration)
                    except:
                        continue
                avg_trade_duration = np.mean(durations) if durations else 0

            # 收益风险比
            avg_win = gross_profit / winning_trades if winning_trades > 0 else 0
            avg_loss = gross_loss / losing_trades if losing_trades > 0 else 0

            # 修复无穷大值问题
            if avg_loss > 0:
                reward_risk_ratio = avg_win / avg_loss
            elif avg_win > 0:
                reward_risk_ratio = 999.99  # 使用大数值代替无穷大
            else:
                reward_risk_ratio = 0

            # 计算夏普比率
            if trades:
                returns = [t['profit'] / initial_balance for t in trades]
                if len(returns) > 1:
                    mean_return = np.mean(returns)
                    std_return = np.std(returns)
                    sharpe_ratio = mean_return / std_return if std_return > 0 else 0
                    # 限制夏普比率的范围，避免极值
                    sharpe_ratio = max(-10, min(10, sharpe_ratio))
                else:
                    sharpe_ratio = 0
            else:
                sharpe_ratio = 0

            # 最终余额
            final_balance_calculated = initial_balance + net_profit

            return {
                # 基本统计
                'total_return': total_return,
                'win_rate': win_rate,
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'break_even_trades': break_even_trades,

                # 盈亏统计
                'gross_profit': gross_profit,
                'gross_loss': gross_loss,
                'net_profit': net_profit,
                'profit_factor': profit_factor,

                # 平均统计
                'average_win': avg_win,
                'average_loss': avg_loss,
                'reward_risk_ratio': reward_risk_ratio,

                # 极值统计
                'max_win': max_win,
                'max_loss': max_loss,
                'max_consecutive_wins': max_consecutive_wins,
                'max_consecutive_losses': max_consecutive_losses,

                # 风险统计
                'max_drawdown': max_drawdown,
                'sharpe_ratio': sharpe_ratio,
                'avg_trade_duration': avg_trade_duration,

                # 余额信息
                'initial_balance': initial_balance,
                'final_balance': final_balance,
                'final_balance_calculated': final_balance_calculated
            }

        except Exception as e:
            logger.error(f"❌ 计算回测统计失败: {e}")
            return {}

    def get_model_by_id(self, model_id: str) -> Dict[str, Any]:
        """根据ID获取模型信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT id, name, model_type, symbol, timeframe, status,
                           model_path, created_at, completed_at
                    FROM deep_learning_models
                    WHERE id = ?
                ''', (model_id,))

                result = cursor.fetchone()
                if result:
                    return {
                        'id': result[0],
                        'name': result[1],
                        'model_type': result[2],
                        'symbol': result[3],
                        'timeframe': result[4],
                        'status': result[5],
                        'model_path': result[6],
                        'created_at': result[7],
                        'completed_at': result[8]
                    }
                else:
                    return None

        except Exception as e:
            logger.error(f"❌ 获取模型信息失败: {e}")
            return None

    def get_auto_trading_status(self, user_id: int) -> Dict[str, Any]:
        """获取自动交易状态"""
        try:
            # 查询数据库中的自动交易会话状态
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT id, model_id, created_at, status
                    FROM ai_trading_sessions
                    WHERE user_id = ? AND status = 'active'
                    ORDER BY created_at DESC
                    LIMIT 1
                ''', (user_id,))

                result = cursor.fetchone()
                if result:
                    session_id, model_id, start_time, status = result

                    # 获取模型信息
                    model_info = self.get_model_by_id(model_id)

                    return {
                        'success': True,
                        'active': True,
                        'session_id': session_id,
                        'model_info': model_info,
                        'start_time': start_time
                    }
                else:
                    return {
                        'success': True,
                        'active': False,
                        'session_id': None,
                        'model_info': None,
                        'start_time': None
                    }

        except Exception as e:
            logger.error(f"❌ 获取自动交易状态失败: {e}")
            return {'success': False, 'error': str(e)}

    def _load_and_run_pytorch_model(self, model_path: str, data: List[Dict],
                                   model_info: Dict, show_confidence: bool, use_gpu: bool) -> List[Dict]:
        """加载并运行真实的PyTorch模型"""
        try:
            import torch
            import numpy as np

            logger.info(f"🔮 加载PyTorch模型: {model_path}")

            # 设置设备
            device = torch.device('cuda' if use_gpu and torch.cuda.is_available() else 'cpu')
            logger.info(f"📱 使用设备: {device}")

            # 加载模型
            try:
                # 首先获取模型配置信息
                model_config = model_info.get('config', {})
                if not model_config:
                    logger.error("❌ 模型配置信息缺失")
                    return []

                # 首先加载状态字典以推断特征数量
                state_dict = torch.load(model_path, map_location=device)

                # 根据模型类型推断输入特征数量
                model_type = model_config.get('model_type', 'lstm').lower()
                feature_count = None

                if model_type == 'cnn_lstm' and 'conv1.weight' in state_dict:
                    # CNN-LSTM模型：从第一个卷积层推断特征数量
                    conv_weight_shape = state_dict['conv1.weight'].shape
                    feature_count = conv_weight_shape[1]  # 输入通道数
                    logger.info(f"📊 从CNN权重推断特征数量: {feature_count} (conv1.weight shape: {conv_weight_shape})")
                elif 'lstm.weight_ih_l0' in state_dict:
                    # LSTM/GRU模型：从LSTM权重推断特征数量
                    lstm_weight_shape = state_dict['lstm.weight_ih_l0'].shape
                    feature_count = lstm_weight_shape[1]  # 输入特征数量
                    logger.info(f"📊 从LSTM权重推断特征数量: {feature_count} (lstm.weight_ih_l0 shape: {lstm_weight_shape})")
                elif 'gru.weight_ih_l0' in state_dict:
                    # GRU模型
                    gru_weight_shape = state_dict['gru.weight_ih_l0'].shape
                    feature_count = gru_weight_shape[1]
                    logger.info(f"📊 从GRU权重推断特征数量: {feature_count} (gru.weight_ih_l0 shape: {gru_weight_shape})")

                if feature_count is None:
                    # 如果无法推断，使用默认值
                    feature_count = 8
                    logger.warning(f"⚠️ 无法从模型权重推断特征数量，使用默认值: {feature_count}")
                    logger.warning(f"⚠️ 可用的权重键: {list(state_dict.keys())[:10]}...")

                logger.info(f"📊 特征配置: {model_config.get('features', {})}")

                # 重新创建模型架构
                model = self._create_model(model_config, feature_count)

                # 加载状态字典到模型
                if isinstance(state_dict, dict) and 'state_dict' not in state_dict:
                    # 直接是状态字典
                    model.load_state_dict(state_dict)
                elif isinstance(state_dict, dict) and 'state_dict' in state_dict:
                    # 包含状态字典的字典
                    model.load_state_dict(state_dict['state_dict'])
                else:
                    # 可能是完整的模型对象
                    model = state_dict

                model.to(device)
                model.eval()
                logger.info(f"✅ 模型加载成功")

            except Exception as load_error:
                logger.error(f"❌ 模型加载失败: {load_error}")
                logger.error(f"模型路径: {model_path}")
                logger.error(f"模型配置: {model_config}")
                return []

            # 准备输入数据
            try:
                logger.info(f"📊 准备推理数据，数据点数: {len(data)}")

                # 将价格数据转换为numpy数组
                price_data = []
                for item in data:
                    # 提取OHLCV数据
                    ohlcv = [
                        float(item.get('open', 0)),
                        float(item.get('high', 0)),
                        float(item.get('low', 0)),
                        float(item.get('close', 0)),
                        float(item.get('volume', 0))
                    ]
                    price_data.append(ohlcv)

                price_array = np.array(price_data, dtype=np.float32)
                logger.info(f"📊 价格数据形状: {price_array.shape}")

                # 使用与训练时相同的特征计算方法
                features = self._calculate_features(price_array, model_config)

                if features is None or len(features) == 0:
                    logger.error("❌ 特征计算失败")
                    return []

                logger.info(f"📊 特征数据形状: {features.shape}")

                # 创建序列数据
                sequence_length = model_config.get('sequence_length', 60)
                if len(features) < sequence_length:
                    logger.warning(f"⚠️ 数据不足，需要{sequence_length}个点，只有{len(features)}个")
                    # 如果数据不足，重复最后的数据点
                    padding_needed = sequence_length - len(features)
                    last_row = features[-1:] if len(features) > 0 else np.zeros((1, features.shape[1]))
                    padding = np.repeat(last_row, padding_needed, axis=0)
                    features = np.vstack([features, padding])

                # 取最后sequence_length个数据点
                sequence_data = features[-sequence_length:]

                # 标准化处理
                mean = np.mean(sequence_data, axis=0)
                std = np.std(sequence_data, axis=0)
                std[std == 0] = 1  # 避免除零
                normalized_data = (sequence_data - mean) / std

                # 转换为PyTorch张量 [batch_size, sequence_length, features]
                input_tensor = torch.FloatTensor(normalized_data).unsqueeze(0).to(device)

                logger.info(f"📊 最终输入张量形状: {input_tensor.shape}")

            except Exception as data_error:
                logger.error(f"❌ 数据预处理失败: {data_error}")
                import traceback
                logger.error(f"详细错误: {traceback.format_exc()}")
                return []

            # 执行推理
            try:
                with torch.no_grad():
                    outputs = model(input_tensor)

                    # 处理模型输出
                    if isinstance(outputs, torch.Tensor):
                        predictions = outputs.cpu().numpy()
                    else:
                        # 如果输出是字典或元组，提取主要预测
                        predictions = outputs[0].cpu().numpy() if isinstance(outputs, (list, tuple)) else outputs.cpu().numpy()

                    logger.info(f"🎯 模型输出形状: {predictions.shape}")

            except Exception as inference_error:
                logger.error(f"❌ 模型推理失败: {inference_error}")
                return []

            # 解析预测结果
            try:
                results = []

                logger.info(f"📊 处理预测结果，输出形状: {predictions.shape}")

                # 处理模型输出
                if len(predictions.shape) == 3:
                    # 形状为 [batch_size, sequence_length, num_classes]
                    predictions = predictions[0]  # 取第一个batch
                elif len(predictions.shape) == 2:
                    # 形状为 [batch_size, num_classes] 或 [sequence_length, num_classes]
                    if predictions.shape[0] == 1:
                        predictions = predictions[0]  # 取第一个batch

                # 确保predictions是2D数组
                if len(predictions.shape) == 1:
                    predictions = predictions.reshape(1, -1)

                # 只取最后一个时间步的预测（最新的预测）
                if len(predictions.shape) == 2 and predictions.shape[0] > 1:
                    final_prediction = predictions[-1]  # 取最后一个时间步
                else:
                    final_prediction = predictions[0] if len(predictions.shape) == 2 else predictions

                logger.info(f"📊 最终预测形状: {final_prediction.shape}")

                # 处理预测结果
                if len(final_prediction) >= 3:
                    # 三分类模型 [SELL, HOLD, BUY]
                    sell_prob = float(final_prediction[0])
                    hold_prob = float(final_prediction[1])
                    buy_prob = float(final_prediction[2])

                    # 应用softmax确保概率和为1
                    import torch.nn.functional as F
                    probs = F.softmax(torch.tensor([sell_prob, hold_prob, buy_prob]), dim=0)
                    sell_prob, hold_prob, buy_prob = probs.tolist()

                    # 确定预测类别
                    max_prob = max(sell_prob, hold_prob, buy_prob)
                    if max_prob == buy_prob:
                        prediction = 'BUY'
                    elif max_prob == sell_prob:
                        prediction = 'SELL'
                    else:
                        prediction = 'HOLD'

                    # 验证置信度范围
                    confidence = self._validate_confidence(max_prob)

                elif len(final_prediction) == 1:
                    # 二分类或回归模型
                    prob = float(final_prediction[0])
                    if prob > 0.6:
                        prediction = 'BUY'
                        confidence = self._validate_confidence(prob)
                    elif prob < 0.4:
                        prediction = 'SELL'
                        confidence = self._validate_confidence(1 - prob)
                    else:
                        prediction = 'HOLD'
                        confidence = 0.5
                else:
                    # 未知格式，使用默认值
                    prediction = 'HOLD'
                    confidence = 0.5

                # 获取当前价格
                current_price = float(data[-1].get('close', 0)) if data else 0

                # 计算目标价格并格式化
                symbol = model_info.get('symbol', 'XAUUSD')
                if prediction == 'BUY':
                    price_target = self._format_price(current_price * 1.01, symbol)  # 上涨1%
                elif prediction == 'SELL':
                    price_target = self._format_price(current_price * 0.99, symbol)  # 下跌1%
                else:  # HOLD
                    price_target = self._format_price(current_price, symbol)  # 保持当前价格

                # 生成分析信息
                if len(data) > 1:
                    price_change = ((current_price - float(data[0].get('close', current_price))) / float(data[0].get('close', current_price))) * 100
                else:
                    price_change = 0

                # 计算波动性
                try:
                    # 获取最近的价格数据计算波动性
                    recent_prices = [float(d.get('close', 0)) for d in data[-min(len(data), 20):] if d.get('close', 0) > 0]

                    if len(recent_prices) >= 3:
                        import numpy as np
                        volatility = (np.std(recent_prices) / np.mean(recent_prices)) * 100 if np.mean(recent_prices) != 0 else 0
                    else:
                        volatility = 1.0  # 默认波动性
                except Exception:
                    volatility = 1.0  # 计算失败时的默认值

                analysis = {
                    'price_change': f"{price_change:.2f}%",
                    'volatility': f"{volatility:.2f}%",
                    'trend': 'upward' if price_change > 1 else 'downward' if price_change < -1 else 'sideways',
                    'signal_strength': 'strong' if confidence > 0.8 else 'medium' if confidence > 0.6 else 'weak',
                    'reason': f"基于深度学习模型分析，置信度{confidence*100:.1f}%"
                }

                result = {
                    'prediction': prediction,
                    'confidence': confidence if show_confidence else None,
                    'current_price': current_price,
                    'price_target': price_target,
                    'timestamp': data[-1].get('timestamp', datetime.now()).isoformat() if data and hasattr(data[-1].get('timestamp', datetime.now()), 'isoformat') else str(datetime.now()),
                    'analysis': analysis
                }

                results.append(result)

                logger.info(f"✅ 生成预测结果: {prediction}, 置信度: {confidence:.3f}")
                return results

            except Exception as parse_error:
                logger.error(f"❌ 结果解析失败: {parse_error}")
                import traceback
                logger.error(f"详细错误: {traceback.format_exc()}")
                return []

        except Exception as e:
            logger.error(f"❌ PyTorch模型推理失败: {e}")
            return []

    def start_auto_trading(self, model_id: str, user_id: int, trading_config: Dict[str, Any]) -> Dict[str, Any]:
        """启动AI自动交易"""
        try:
            logger.info(f"🤖 启动AI自动交易: 模型={model_id[:8]}..., 用户={user_id}")

            # 验证模型状态
            model_detail = self.get_model_detail(model_id, user_id)
            if not model_detail['success']:
                return {'success': False, 'error': '模型不存在或无权限访问'}

            model = model_detail['model']
            if model['status'] != 'completed':
                return {'success': False, 'error': '模型未完成训练，无法用于自动交易'}

            # 保存自动交易配置到数据库
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 检查是否已有活跃的自动交易
            cursor.execute('''
                SELECT id FROM ai_trading_sessions
                WHERE user_id = ? AND status = 'active'
            ''', (user_id,))

            if cursor.fetchone():
                conn.close()
                return {'success': False, 'error': '已有活跃的自动交易会话，请先停止'}

            # 创建新的自动交易会话
            session_id = str(uuid.uuid4())
            cursor.execute('''
                INSERT INTO ai_trading_sessions
                (id, user_id, model_id, trading_config, status, created_at)
                VALUES (?, ?, ?, ?, 'active', ?)
            ''', (session_id, user_id, model_id, json.dumps(trading_config), datetime.now().isoformat()))

            conn.commit()
            conn.close()

            logger.info(f"✅ AI自动交易启动成功: 会话={session_id[:8]}...")

            return {
                'success': True,
                'session_id': session_id,
                'message': 'AI自动交易已启动'
            }

        except Exception as e:
            logger.error(f"❌ 启动AI自动交易失败: {e}")
            return {'success': False, 'error': str(e)}

    def stop_auto_trading(self, user_id: int) -> Dict[str, Any]:
        """停止AI自动交易"""
        try:
            logger.info(f"🛑 停止AI自动交易: 用户={user_id}")

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 更新活跃会话状态
            cursor.execute('''
                UPDATE ai_trading_sessions
                SET status = 'stopped', stopped_at = ?
                WHERE user_id = ? AND status = 'active'
            ''', (datetime.now().isoformat(), user_id))

            affected_rows = cursor.rowcount
            conn.commit()
            conn.close()

            if affected_rows > 0:
                logger.info(f"✅ AI自动交易停止成功")
                return {
                    'success': True,
                    'message': 'AI自动交易已停止'
                }
            else:
                return {
                    'success': False,
                    'error': '没有找到活跃的自动交易会话'
                }

        except Exception as e:
            logger.error(f"❌ 停止AI自动交易失败: {e}")
            return {'success': False, 'error': str(e)}

    def execute_ai_trade(self, user_id: int, trade_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行AI推理交易（支持悬崖勒马功能）"""
        try:
            logger.info(f"💰 执行AI交易: {trade_data.get('action')} {trade_data.get('lot_size')}手")

            # 验证交易参数
            required_fields = ['symbol', 'action', 'lot_size']
            for field in required_fields:
                if field not in trade_data:
                    return {'success': False, 'error': f'缺少必需参数: {field}'}

            symbol = trade_data['symbol']
            action = trade_data['action']  # BUY or SELL
            lot_size = trade_data['lot_size']

            # 获取交易配置（包含风险管理参数）
            trading_config = trade_data.get('trading_config', {})
            stop_loss_pips = trading_config.get('stop_loss_pips', 50)
            take_profit_pips = trading_config.get('take_profit_pips', 100)
            dynamic_sl = trading_config.get('dynamic_sl', True)
            trailing_stop = trading_config.get('trailing_stop', False)
            cliff_brake_enabled = trading_config.get('cliff_brake_enabled', False)  # 悬崖勒马开关

            # 获取推理结果用于动态调整
            inference_result = trade_data.get('inference_result', {})

            # 调用MT5服务执行交易
            from services.mt5_service import mt5_service

            if not mt5_service.is_connected():
                return {'success': False, 'error': 'MT5未连接'}

            # 获取当前价格
            tick = mt5_service.get_symbol_info_tick(symbol)
            if not tick:
                return {'success': False, 'error': f'无法获取{symbol}的价格信息'}

            # 悬崖勒马功能检测
            original_action = action
            cliff_brake_triggered = False

            if cliff_brake_enabled:
                # 获取最近的AI交易历史
                recent_trades = self._get_recent_ai_trades(user_id, symbol, limit=10)

                if len(recent_trades) >= 2:
                    # 使用当前价格进行悬崖勒马检测
                    current_price = tick.bid if action == 'SELL' else tick.ask
                    cliff_brake_result = self._check_cliff_brake(recent_trades, current_price, action)

                    if cliff_brake_result['should_reverse']:
                        # 反转交易方向
                        action = 'SELL' if action == 'BUY' else 'BUY'
                        cliff_brake_triggered = True

                        logger.info(f"🚨 实盘悬崖勒马触发: {original_action} -> {action}")
                        logger.info(f"   原因: {cliff_brake_result['reason']}")
                        logger.info(f"   价格趋势: {cliff_brake_result['price_trend']}")

                    elif cliff_brake_result['should_skip']:
                        # 跳过这次交易
                        logger.info(f"⏭️ 实盘悬崖勒马跳过交易: {cliff_brake_result['reason']}")
                        return {
                            'success': False,
                            'error': f"悬崖勒马跳过交易: {cliff_brake_result['reason']}",
                            'cliff_brake_skip': True
                        }

            # 动态调整止盈止损
            if dynamic_sl:
                adjusted_sl_tp = self._calculate_dynamic_stop_loss_take_profit(
                    symbol, tick, action, stop_loss_pips, take_profit_pips,
                    inference_result, trading_config
                )
                stop_loss_pips = adjusted_sl_tp['stop_loss_pips']
                take_profit_pips = adjusted_sl_tp['take_profit_pips']
                logger.info(f"🔧 动态调整: 止损{stop_loss_pips}pips, 止盈{take_profit_pips}pips")

            # 计算止损止盈价格
            if action == 'BUY':
                entry_price = tick.ask
                sl_price = entry_price - (stop_loss_pips * mt5_service.get_point(symbol))
                tp_price = entry_price + (take_profit_pips * mt5_service.get_point(symbol))
                order_type = 'BUY'
            else:  # SELL
                entry_price = tick.bid
                sl_price = entry_price + (stop_loss_pips * mt5_service.get_point(symbol))
                tp_price = entry_price - (take_profit_pips * mt5_service.get_point(symbol))
                order_type = 'SELL'

            # 执行交易
            trade_result = mt5_service.place_order(
                symbol=symbol,
                order_type=order_type,
                lot_size=lot_size,
                price=entry_price,
                sl=sl_price,
                tp=tp_price,
                comment=f"AI_Trade_{trade_data.get('inference_result', {}).get('confidence', 0):.2f}"
            )

            if trade_result['success']:
                # 记录交易到数据库（包含悬崖勒马信息）
                enhanced_trade_data = trade_data.copy()
                enhanced_trade_data['action'] = action  # 使用可能被悬崖勒马修改后的方向
                enhanced_trade_data['cliff_brake_info'] = {
                    'enabled': cliff_brake_enabled,
                    'triggered': cliff_brake_triggered,
                    'original_action': original_action,
                    'final_action': action
                }

                self._record_ai_trade(user_id, enhanced_trade_data, trade_result)

                success_message = f'AI交易执行成功: {action} {lot_size}手'
                if cliff_brake_triggered:
                    success_message += f' (悬崖勒马: {original_action}→{action})'

                logger.info(f"✅ {success_message}: 订单={trade_result.get('order_id')}")

                return {
                    'success': True,
                    'order_id': trade_result.get('order_id'),
                    'entry_price': entry_price,
                    'sl_price': sl_price,
                    'tp_price': tp_price,
                    'message': success_message,
                    'cliff_brake_triggered': cliff_brake_triggered,
                    'original_action': original_action,
                    'final_action': action
                }
            else:
                return {
                    'success': False,
                    'error': f'交易执行失败: {trade_result.get("error")}'
                }

        except Exception as e:
            logger.error(f"❌ 执行AI交易失败: {e}")
            return {'success': False, 'error': str(e)}

    def _calculate_dynamic_stop_loss_take_profit(self, symbol: str, tick, action: str,
                                               base_sl_pips: int, base_tp_pips: int,
                                               inference_result: Dict, trading_config: Dict) -> Dict:
        """计算动态止盈止损"""
        try:
            # 获取市场波动性
            from services.mt5_service import mt5_service

            # 获取最近的价格数据计算波动性
            rates = mt5_service.get_historical_data(symbol, '15m', 20)
            if not rates or len(rates) < 10:
                logger.warning("无法获取历史数据，使用基础止盈止损")
                return {
                    'stop_loss_pips': base_sl_pips,
                    'take_profit_pips': base_tp_pips
                }

            # 计算价格波动性
            prices = [float(rate['close']) for rate in rates[-10:]]
            import numpy as np
            volatility = np.std(prices) / np.mean(prices) if len(prices) > 1 else 0.01

            # 获取推理置信度
            confidence = inference_result.get('confidence', 0.5)

            # 动态调整因子
            volatility_factor = max(0.5, min(2.0, volatility * 100))  # 0.5-2.0倍
            confidence_factor = max(0.7, min(1.5, confidence * 2))    # 0.7-1.5倍

            # 计算调整后的止盈止损
            adjusted_sl = int(base_sl_pips * volatility_factor)
            adjusted_tp = int(base_tp_pips * volatility_factor * confidence_factor)

            # 限制范围
            adjusted_sl = max(10, min(200, adjusted_sl))  # 10-200 pips
            adjusted_tp = max(20, min(500, adjusted_tp))  # 20-500 pips

            logger.info(f"📊 动态调整参数: 波动性{volatility:.4f}, 置信度{confidence:.2f}")
            logger.info(f"🔧 调整因子: 波动性{volatility_factor:.2f}, 置信度{confidence_factor:.2f}")

            return {
                'stop_loss_pips': adjusted_sl,
                'take_profit_pips': adjusted_tp,
                'volatility': volatility,
                'confidence': confidence,
                'volatility_factor': volatility_factor,
                'confidence_factor': confidence_factor
            }

        except Exception as e:
            logger.error(f"❌ 动态止盈止损计算失败: {e}")
            return {
                'stop_loss_pips': base_sl_pips,
                'take_profit_pips': base_tp_pips
            }

    def _record_ai_trade(self, user_id: int, trade_data: Dict, trade_result: Dict):
        """记录AI交易到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 确保ai_trades表存在（包含悬崖勒马所需字段）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS ai_trades (
                    id TEXT PRIMARY KEY,
                    user_id INTEGER NOT NULL,
                    symbol TEXT NOT NULL,
                    action TEXT NOT NULL,
                    lot_size REAL NOT NULL,
                    entry_price REAL,
                    close_price REAL,
                    sl_price REAL,
                    tp_price REAL,
                    order_id TEXT,
                    inference_result TEXT,
                    trading_config TEXT,
                    status TEXT DEFAULT 'open',
                    created_at TEXT NOT NULL,
                    closed_at TEXT,
                    profit REAL DEFAULT 0,
                    cliff_brake_info TEXT
                )
            ''')

            # 检查并添加缺失的字段（为了兼容旧表）
            try:
                cursor.execute("ALTER TABLE ai_trades ADD COLUMN close_price REAL")
            except:
                pass  # 字段已存在

            try:
                cursor.execute("ALTER TABLE ai_trades ADD COLUMN cliff_brake_info TEXT")
            except:
                pass  # 字段已存在

            trade_id = str(uuid.uuid4())
            cursor.execute('''
                INSERT INTO ai_trades
                (id, user_id, symbol, action, lot_size, entry_price, sl_price, tp_price,
                 order_id, inference_result, trading_config, cliff_brake_info, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                trade_id, user_id, trade_data['symbol'], trade_data['action'],
                trade_data['lot_size'], trade_result.get('entry_price'),
                trade_result.get('sl_price'), trade_result.get('tp_price'),
                trade_result.get('order_id'),
                json.dumps(trade_data.get('inference_result', {})),
                json.dumps(trade_data.get('trading_config', {})),
                json.dumps(trade_data.get('cliff_brake_info', {})),
                datetime.now().isoformat()
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"❌ 记录AI交易失败: {e}")

    def get_trading_statistics(self, user_id: int) -> Dict[str, Any]:
        """获取交易统计"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 今日交易数量
            today = datetime.now().date().isoformat()
            cursor.execute('''
                SELECT COUNT(*) FROM ai_trades
                WHERE user_id = ? AND DATE(created_at) = ?
            ''', (user_id, today))
            today_trades = cursor.fetchone()[0]

            # 当前持仓数量
            cursor.execute('''
                SELECT COUNT(*) FROM ai_trades
                WHERE user_id = ? AND status = 'open'
            ''', (user_id,))
            current_positions = cursor.fetchone()[0]

            # 总盈亏
            cursor.execute('''
                SELECT COALESCE(SUM(profit), 0) FROM ai_trades
                WHERE user_id = ? AND status = 'closed'
            ''', (user_id,))
            total_profit = cursor.fetchone()[0]

            conn.close()

            return {
                'success': True,
                'statistics': {
                    'todayTrades': today_trades,
                    'currentPositions': current_positions,
                    'totalProfit': total_profit
                }
            }

        except Exception as e:
            logger.error(f"❌ 获取交易统计失败: {e}")
            return {'success': False, 'error': str(e)}

    def get_position_details(self, user_id: int) -> Dict[str, Any]:
        """获取持仓详情"""
        try:
            from services.mt5_service import mt5_service

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 获取所有开仓的AI交易
            cursor.execute('''
                SELECT id, symbol, action, lot_size, entry_price, sl_price, tp_price,
                       order_id, created_at, inference_result
                FROM ai_trades
                WHERE user_id = ? AND status = 'open'
                ORDER BY created_at DESC
            ''', (user_id,))

            trades = cursor.fetchall()
            positions = []

            for trade in trades:
                trade_id, symbol, action, lot_size, entry_price, sl_price, tp_price, order_id, created_at, inference_result = trade

                # 获取当前价格
                current_price = None
                current_profit = 0

                if mt5_service.is_connected():
                    tick = mt5_service.get_symbol_info_tick(symbol)
                    if tick:
                        current_price = tick.bid if action == 'buy' else tick.ask

                        # 计算当前盈亏
                        if entry_price and current_price:
                            if action == 'buy':
                                current_profit = (current_price - entry_price) * lot_size * 100000
                            else:
                                current_profit = (entry_price - current_price) * lot_size * 100000

                positions.append({
                    'trade_id': trade_id,
                    'symbol': symbol,
                    'direction': action,
                    'volume': lot_size,
                    'open_price': entry_price,
                    'current_price': current_price,
                    'stop_loss': sl_price,
                    'take_profit': tp_price,
                    'current_profit': current_profit,
                    'open_time': created_at,
                    'order_id': order_id
                })

            conn.close()

            return {
                'success': True,
                'positions': positions
            }

        except Exception as e:
            logger.error(f"❌ 获取持仓详情失败: {e}")
            return {'success': False, 'error': str(e)}

    def close_position(self, user_id: int, position_id: str) -> Dict[str, Any]:
        """平仓单个持仓"""
        try:
            logger.info(f"🔄 平仓单个持仓: 用户={user_id}, 持仓ID={position_id}")

            from services.mt5_service import mt5_service

            if not mt5_service.is_connected():
                return {'success': False, 'error': 'MT5未连接'}

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 获取交易信息
            cursor.execute('''
                SELECT id, order_id, symbol, action FROM ai_trades
                WHERE user_id = ? AND id = ? AND status = 'open'
            ''', (user_id, position_id))

            trade = cursor.fetchone()
            if not trade:
                conn.close()
                return {'success': False, 'error': '持仓不存在或已平仓'}

            trade_id, order_id, symbol, action = trade

            # 执行平仓
            if order_id:
                close_result = mt5_service.close_position(int(order_id))
                if close_result['success']:
                    # 更新数据库状态
                    cursor.execute('''
                        UPDATE ai_trades
                        SET status = 'closed', closed_at = ?, profit = ?
                        WHERE id = ?
                    ''', (datetime.now().isoformat(), close_result.get('profit', 0), trade_id))

                    conn.commit()
                    conn.close()

                    logger.info(f"✅ 持仓平仓成功: {position_id}")
                    return {'success': True, 'message': '持仓已平仓'}
                else:
                    conn.close()
                    return {'success': False, 'error': f'平仓失败: {close_result.get("error", "未知错误")}'}
            else:
                conn.close()
                return {'success': False, 'error': '无效的订单ID'}

        except Exception as e:
            logger.error(f"❌ 平仓失败: {e}")
            return {'success': False, 'error': str(e)}

    def close_all_ai_positions(self, user_id: int) -> Dict[str, Any]:
        """平仓所有AI交易持仓"""
        try:
            logger.info(f"🔄 平仓所有AI持仓: 用户={user_id}")

            from services.mt5_service import mt5_service

            if not mt5_service.is_connected():
                return {'success': False, 'error': 'MT5未连接'}

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 获取所有开仓的AI交易
            cursor.execute('''
                SELECT id, order_id, symbol, action FROM ai_trades
                WHERE user_id = ? AND status = 'open' AND order_id IS NOT NULL
            ''', (user_id,))

            open_trades = cursor.fetchall()
            closed_count = 0

            for trade in open_trades:
                trade_id, order_id, symbol, action = trade

                # 平仓
                close_result = mt5_service.close_position(order_id)

                if close_result['success']:
                    # 更新数据库状态
                    cursor.execute('''
                        UPDATE ai_trades
                        SET status = 'closed', closed_at = ?, profit = ?
                        WHERE id = ?
                    ''', (datetime.now().isoformat(), close_result.get('profit', 0), trade_id))

                    closed_count += 1

            conn.commit()
            conn.close()

            logger.info(f"✅ 平仓完成: {closed_count}/{len(open_trades)} 个持仓")

            return {
                'success': True,
                'closed_count': closed_count,
                'total_positions': len(open_trades),
                'message': f'已平仓 {closed_count} 个持仓'
            }

        except Exception as e:
            logger.error(f"❌ 平仓所有持仓失败: {e}")
            return {'success': False, 'error': str(e)}

    def _ensure_trading_tables(self):
        """确保交易相关表存在"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # AI交易会话表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS ai_trading_sessions (
                    id TEXT PRIMARY KEY,
                    user_id INTEGER NOT NULL,
                    model_id TEXT NOT NULL,
                    trading_config TEXT,
                    status TEXT DEFAULT 'active',
                    created_at TEXT NOT NULL,
                    stopped_at TEXT
                )
            ''')

            # AI交易记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS ai_trades (
                    id TEXT PRIMARY KEY,
                    user_id INTEGER NOT NULL,
                    symbol TEXT NOT NULL,
                    action TEXT NOT NULL,
                    lot_size REAL NOT NULL,
                    entry_price REAL,
                    sl_price REAL,
                    tp_price REAL,
                    order_id TEXT,
                    inference_result TEXT,
                    trading_config TEXT,
                    status TEXT DEFAULT 'open',
                    created_at TEXT NOT NULL,
                    closed_at TEXT,
                    profit REAL DEFAULT 0
                )
            ''')

            # 参数优化结果表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS parameter_optimization_results (
                    id TEXT PRIMARY KEY,
                    user_id INTEGER NOT NULL,
                    model_id TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    timeframe TEXT NOT NULL,
                    optimization_period TEXT NOT NULL,
                    risk_preference TEXT NOT NULL,
                    optimization_results TEXT NOT NULL,
                    best_parameters TEXT NOT NULL,
                    total_combinations INTEGER NOT NULL,
                    successful_combinations INTEGER NOT NULL,
                    date_range TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES user (id),
                    FOREIGN KEY (model_id) REFERENCES deep_learning_models (id)
                )
            ''')

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"❌ 创建交易表失败: {e}")

# 全局深度学习服务实例
deep_learning_service = DeepLearningService()
