{% extends "base.html" %}

{% block title %}模型训练{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-dumbbell text-primary me-2"></i>
                    深度学习模型训练
                </h1>
                <a href="{{ url_for('deep_learning_dashboard') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>返回仪表板
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 训练配置 -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-cog me-2"></i>训练配置
                    </h6>
                </div>
                <div class="card-body">
                    <form id="trainingForm">
                        <!-- 基本配置 -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">模型名称</label>
                                <input type="text" class="form-control" id="modelName" required>
                                <div class="form-text">为您的模型起一个有意义的名称</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">模型类型</label>
                                <select class="form-select" id="modelType" required onchange="showModelTypeDescription()">
                                    <option value="">请选择模型类型</option>
                                    <option value="lstm">LSTM (长短期记忆网络)</option>
                                    <option value="gru">GRU (门控循环单元)</option>
                                    <option value="transformer">Transformer</option>
                                    <option value="cnn_lstm">CNN-LSTM混合模型</option>
                                    <option value="attention_lstm">注意力LSTM</option>
                                </select>
                            </div>
                        </div>

                        <!-- 模型类型说明 -->
                        <div class="mb-3" id="modelTypeDescription" style="display: none;">
                            <div class="card border-info">
                                <div class="card-body">
                                    <h6 class="card-title text-info">
                                        <i class="fas fa-info-circle"></i>
                                        <span id="modelTypeName">模型说明</span>
                                    </h6>
                                    <div id="modelTypeContent">
                                        <!-- 动态内容 -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 数据配置 -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">交易品种</label>
                                <select class="form-select" id="symbol">
                                    <option value="XAUUSD">XAU/USD (黄金)</option>
                                    <option value="EURUSD">EUR/USD</option>
                                    <option value="GBPUSD">GBP/USD</option>
                                    <option value="USDJPY">USD/JPY</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">时间框架</label>
                                <select class="form-select" id="timeframe">
                                    <option value="1m">1分钟</option>
                                    <option value="5m">5分钟</option>
                                    <option value="15m">15分钟</option>
                                    <option value="1h" selected>1小时</option>
                                    <option value="4h">4小时</option>
                                    <option value="1d">1天</option>
                                </select>
                            </div>
                        </div>

                        <!-- 训练数据时间范围 -->
                        <div class="mb-3">
                            <label class="form-label">训练数据时间范围</label>
                            <div class="card border-light bg-light">
                                <div class="card-body p-3">
                                    <!-- 选择模式 -->
                                    <div class="row mb-3">
                                        <div class="col-12">
                                            <div class="btn-group w-100" role="group" id="dateRangeMode">
                                                <input type="radio" class="btn-check" name="dateMode" id="dateModeDays" value="days" checked>
                                                <label class="btn btn-outline-primary" for="dateModeDays">
                                                    <i class="fas fa-calendar-day me-1"></i>按天数
                                                </label>

                                                <input type="radio" class="btn-check" name="dateMode" id="dateModeRange" value="range">
                                                <label class="btn btn-outline-primary" for="dateModeRange">
                                                    <i class="fas fa-calendar-alt me-1"></i>选择日期范围
                                                </label>

                                                <input type="radio" class="btn-check" name="dateMode" id="dateModePreset" value="preset">
                                                <label class="btn btn-outline-primary" for="dateModePreset">
                                                    <i class="fas fa-bookmark me-1"></i>预设范围
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 按天数模式 -->
                                    <div id="daysMode" class="date-mode-content">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <label class="form-label">训练数据天数</label>
                                                <input type="number" class="form-control" id="trainingDays" value="365" min="30" max="1825">
                                                <div class="form-text">从今天往前推算的天数</div>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">数据结束日期</label>
                                                <input type="date" class="form-control" id="endDate">
                                                <div class="form-text">留空则使用最新数据</div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 日期范围模式 -->
                                    <div id="rangeMode" class="date-mode-content" style="display: none;">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <label class="form-label">开始日期</label>
                                                <input type="date" class="form-control" id="startDate" required>
                                                <div class="form-text">训练数据开始日期</div>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">结束日期</label>
                                                <input type="date" class="form-control" id="endDateRange" required>
                                                <div class="form-text">训练数据结束日期</div>
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-12">
                                                <div class="alert alert-info py-2 mb-0">
                                                    <small>
                                                        <i class="fas fa-info-circle me-1"></i>
                                                        <span id="dateRangeInfo">请选择日期范围</span>
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 预设范围模式 -->
                                    <div id="presetMode" class="date-mode-content" style="display: none;">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <label class="form-label">预设时间范围</label>
                                                <select class="form-select" id="presetRange">
                                                    <option value="">请选择预设范围</option>
                                                    <option value="1m">最近1个月</option>
                                                    <option value="3m">最近3个月</option>
                                                    <option value="6m">最近6个月</option>
                                                    <option value="1y">最近1年</option>
                                                    <option value="2y">最近2年</option>
                                                    <option value="3y">最近3年</option>
                                                    <option value="5y">最近5年</option>
                                                    <option value="ytd">今年至今</option>
                                                    <option value="last_year">去年全年</option>
                                                    <option value="bull_market">牛市期间 (2020-2021)</option>
                                                    <option value="bear_market">熊市期间 (2022)</option>
                                                    <option value="covid_period">疫情期间 (2020年3月-12月)</option>
                                                </select>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">数据质量</label>
                                                <select class="form-select" id="dataQuality">
                                                    <option value="all">包含所有数据</option>
                                                    <option value="trading_hours">仅交易时间</option>
                                                    <option value="high_volume">高成交量时段</option>
                                                    <option value="exclude_holidays">排除节假日</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-12">
                                                <div class="alert alert-success py-2 mb-0">
                                                    <small>
                                                        <i class="fas fa-check-circle me-1"></i>
                                                        <span id="presetRangeInfo">选择预设范围以查看详情</span>
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 模型参数 -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label class="form-label">序列长度</label>
                                <input type="number" class="form-control" id="sequenceLength" value="60" min="10" max="200">
                                <div class="form-text">用于预测的历史数据点数</div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">隐藏层大小</label>
                                <input type="number" class="form-control" id="hiddenSize" value="128" min="32" max="512">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">层数</label>
                                <input type="number" class="form-control" id="numLayers" value="2" min="1" max="5">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Dropout率</label>
                                <input type="number" class="form-control" id="dropout" value="0.2" min="0" max="0.8" step="0.1">
                            </div>
                        </div>

                        <!-- 训练参数 -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label class="form-label">批次大小</label>
                                <input type="number" class="form-control" id="batchSize" value="32" min="8" max="256">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">学习率</label>
                                <input type="number" class="form-control" id="learningRate" value="0.001" min="0.0001" max="0.1" step="0.0001">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">训练轮数</label>
                                <input type="number" class="form-control" id="epochs" value="100" min="10" max="1000">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">早停耐心</label>
                                <input type="number" class="form-control" id="patience" value="20" min="5" max="100" title="验证损失连续多少轮不改善时停止训练">
                            </div>
                        </div>

                        <!-- 早停配置 -->
                        <div class="mb-3">
                            <label class="form-label">早停配置</label>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="earlyStoppingEnabled" checked>
                                        <label class="form-check-label" for="earlyStoppingEnabled">启用早停机制</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">最少训练轮次</label>
                                    <input type="number" class="form-control" id="minEpochs" value="20" min="5" max="100" title="至少训练多少轮才能触发早停">
                                </div>
                                <div class="col-md-4">
                                    <small class="text-muted">早停可以防止过拟合，但可能导致训练提前结束</small>
                                </div>
                            </div>
                        </div>

                        <!-- 特征选择 -->
                        <div class="mb-3">
                            <label class="form-label">特征选择</label>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="usePrice" checked>
                                        <label class="form-check-label" for="usePrice">价格数据 (OHLC)</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="useVolume" checked>
                                        <label class="form-check-label" for="useVolume">成交量</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="useTechnical" checked>
                                        <label class="form-check-label" for="useTechnical">技术指标</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="useTime" checked>
                                        <label class="form-check-label" for="useTime">时间特征</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- GPU设置 -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="useGPU" checked>
                                    <label class="form-check-label" for="useGPU">
                                        使用GPU加速训练
                                    </label>
                                </div>
                                <div class="form-text">如果可用，将使用GPU进行训练加速</div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="saveCheckpoints" checked>
                                    <label class="form-check-label" for="saveCheckpoints">
                                        保存训练检查点
                                    </label>
                                </div>
                                <div class="form-text">定期保存模型状态，便于恢复训练</div>
                            </div>
                        </div>

                        <!-- 任务状态信息 -->
                        <div id="taskStatusInfo" class="mb-3" style="display: none;"></div>

                        <!-- 操作按钮 -->
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary" id="startDataPrepBtn">
                                <i class="fas fa-database me-1"></i>开始数据准备
                            </button>
                            <button type="button" class="btn btn-success" id="startModelTrainingBtn" style="display: none;">
                                <i class="fas fa-play me-1"></i>开始模型训练
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                <i class="fas fa-undo me-1"></i>重置
                            </button>
                            <button type="button" class="btn btn-info" onclick="loadPreset()">
                                <i class="fas fa-magic me-1"></i>加载预设
                            </button>
                            <button type="button" class="btn btn-outline-info" onclick="checkCurrentTaskStatus()">
                                <i class="fas fa-search me-1"></i>检查状态
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 训练状态和系统信息 -->
        <div class="col-xl-4 col-lg-5">
            <!-- GPU状态 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-microchip me-2"></i>GPU状态
                    </h6>
                </div>
                <div class="card-body">
                    <div id="gpuStatus">
                        <div class="text-center py-3">
                            <i class="fas fa-spinner fa-spin fa-2x text-muted mb-2"></i>
                            <p class="text-muted">检查GPU状态...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 训练进度 -->
            <div class="card shadow mb-4" id="trainingProgressCard" style="display: none;">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-line me-2"></i>训练进度
                    </h6>
                </div>
                <div class="card-body">
                    <div id="trainingProgress">
                        <!-- 训练进度将在这里显示 -->
                    </div>

                    <!-- 训练控制按钮 -->
                    <div id="trainingControls" class="mt-3" style="display: none;">
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-warning btn-sm" id="pauseTrainingBtn" onclick="pauseTraining()">
                                <i class="fas fa-pause me-1"></i>暂停
                            </button>
                            <button type="button" class="btn btn-success btn-sm" id="resumeTrainingBtn" onclick="resumeTraining()" style="display: none;">
                                <i class="fas fa-play me-1"></i>恢复
                            </button>
                            <button type="button" class="btn btn-danger btn-sm" id="stopTrainingBtn" onclick="stopTraining()">
                                <i class="fas fa-stop me-1"></i>停止
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 预设配置 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bookmark me-2"></i>预设配置
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary btn-sm" onclick="loadPreset('quick')">
                            <i class="fas fa-bolt me-1"></i>快速训练
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="loadPreset('balanced')">
                            <i class="fas fa-balance-scale me-1"></i>平衡配置
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="loadPreset('high_accuracy')">
                            <i class="fas fa-bullseye me-1"></i>高精度
                        </button>
                        <button class="btn btn-outline-warning btn-sm" onclick="loadPreset('experimental')">
                            <i class="fas fa-flask me-1"></i>实验性
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let trainingTaskId = null;
let progressInterval = null;
let gpuStatusInterval = null;

// 页面加载时检查GPU状态
document.addEventListener('DOMContentLoaded', function() {
    checkGPUStatus();
    initializeDateRangeSelector();
    restoreTrainingState();
});

// 恢复训练状态
function restoreTrainingState() {
    // 从localStorage恢复训练状态
    const savedState = localStorage.getItem('trainingState');
    if (savedState) {
        try {
            const state = JSON.parse(savedState);

            if (state.taskId && state.status !== 'completed' && state.status !== 'failed') {
                trainingTaskId = state.taskId;

                // 恢复表单数据
                if (state.formData) {
                    restoreFormData(state.formData);
                }

                // 显示训练进度和控制按钮
                showTrainingProgress();

                // 开始监控进度
                startProgressMonitoring();

                // 禁用开始按钮
                const startBtn = document.getElementById('startTrainingBtn');
                startBtn.disabled = true;
                startBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>训练中...';

                showAlert('检测到未完成的训练任务，已恢复状态', 'info');
            }
        } catch (error) {
            console.error('恢复训练状态失败:', error);
            localStorage.removeItem('trainingState');
        }
    }
}

// 保存训练状态
function saveTrainingState(taskId, formData, status = 'running') {
    const state = {
        taskId: taskId,
        formData: formData,
        status: status,
        timestamp: new Date().toISOString()
    };

    localStorage.setItem('trainingState', JSON.stringify(state));
}

// 清除训练状态
function clearTrainingState() {
    localStorage.removeItem('trainingState');
}

// 恢复表单数据
function restoreFormData(formData) {
    try {
        // 恢复基本字段
        if (formData.model_name) document.getElementById('modelName').value = formData.model_name;
        if (formData.model_type) document.getElementById('modelType').value = formData.model_type;
        if (formData.symbol) document.getElementById('symbol').value = formData.symbol;
        if (formData.timeframe) document.getElementById('timeframe').value = formData.timeframe;

        // 恢复模型参数
        if (formData.sequence_length) document.getElementById('sequenceLength').value = formData.sequence_length;
        if (formData.hidden_size) document.getElementById('hiddenSize').value = formData.hidden_size;
        if (formData.num_layers) document.getElementById('numLayers').value = formData.num_layers;
        if (formData.dropout) document.getElementById('dropout').value = formData.dropout;

        // 恢复训练参数
        if (formData.batch_size) document.getElementById('batchSize').value = formData.batch_size;
        if (formData.learning_rate) document.getElementById('learningRate').value = formData.learning_rate;
        if (formData.epochs) document.getElementById('epochs').value = formData.epochs;
        if (formData.patience) document.getElementById('patience').value = formData.patience;
        if (formData.min_epochs) document.getElementById('minEpochs').value = formData.min_epochs;

        // 恢复开关状态
        if (typeof formData.use_gpu === 'boolean') document.getElementById('useGPU').checked = formData.use_gpu;
        if (typeof formData.save_checkpoints === 'boolean') document.getElementById('saveCheckpoints').checked = formData.save_checkpoints;
        if (typeof formData.early_stopping === 'boolean') document.getElementById('earlyStoppingEnabled').checked = formData.early_stopping;

        // 恢复特征选择
        if (formData.features) {
            if (typeof formData.features.price === 'boolean') document.getElementById('usePrice').checked = formData.features.price;
            if (typeof formData.features.volume === 'boolean') document.getElementById('useVolume').checked = formData.features.volume;
            if (typeof formData.features.technical === 'boolean') document.getElementById('useTechnical').checked = formData.features.technical;
            if (typeof formData.features.time === 'boolean') document.getElementById('useTime').checked = formData.features.time;
        }

        // 恢复日期配置
        if (formData.data_config) {
            restoreDateConfig(formData.data_config);
        }

    } catch (error) {
        console.error('恢复表单数据失败:', error);
    }
}

// 恢复日期配置
function restoreDateConfig(dataConfig) {
    try {
        const mode = dataConfig.mode || 'days';

        // 设置日期模式
        document.querySelector(`input[name="dateMode"][value="${mode}"]`).checked = true;
        switchDateMode(mode);

        if (mode === 'days') {
            if (dataConfig.training_days) document.getElementById('trainingDays').value = dataConfig.training_days;
            if (dataConfig.end_date) document.getElementById('endDate').value = dataConfig.end_date;
        } else if (mode === 'range') {
            if (dataConfig.start_date) document.getElementById('startDate').value = dataConfig.start_date;
            if (dataConfig.end_date) document.getElementById('endDateRange').value = dataConfig.end_date;
        } else if (mode === 'preset') {
            if (dataConfig.preset_range) document.getElementById('presetRange').value = dataConfig.preset_range;
            if (dataConfig.data_quality) document.getElementById('dataQuality').value = dataConfig.data_quality;
        }
    } catch (error) {
        console.error('恢复日期配置失败:', error);
    }
}

// 初始化日期范围选择器
function initializeDateRangeSelector() {
    // 设置默认日期
    const today = new Date();
    const oneYearAgo = new Date(today.getFullYear() - 1, today.getMonth(), today.getDate());

    // 设置默认值
    document.getElementById('endDate').value = today.toISOString().split('T')[0];
    document.getElementById('startDate').value = oneYearAgo.toISOString().split('T')[0];
    document.getElementById('endDateRange').value = today.toISOString().split('T')[0];

    // 监听日期模式切换
    document.querySelectorAll('input[name="dateMode"]').forEach(radio => {
        radio.addEventListener('change', function() {
            switchDateMode(this.value);
        });
    });

    // 监听日期范围变化
    document.getElementById('startDate').addEventListener('change', updateDateRangeInfo);
    document.getElementById('endDateRange').addEventListener('change', updateDateRangeInfo);

    // 监听预设范围变化
    document.getElementById('presetRange').addEventListener('change', function() {
        updatePresetRangeInfo(this.value);
    });

    // 监听训练天数变化
    document.getElementById('trainingDays').addEventListener('input', function() {
        updateDaysInfo();
    });

    // 初始化信息显示
    updateDaysInfo();
}

// 切换日期模式
function switchDateMode(mode) {
    // 隐藏所有模式内容
    document.querySelectorAll('.date-mode-content').forEach(content => {
        content.style.display = 'none';
    });

    // 显示选中的模式
    const modeMap = {
        'days': 'daysMode',
        'range': 'rangeMode',
        'preset': 'presetMode'
    };

    const targetMode = modeMap[mode];
    if (targetMode) {
        document.getElementById(targetMode).style.display = 'block';
    }

    // 更新相应的信息显示
    if (mode === 'days') {
        updateDaysInfo();
    } else if (mode === 'range') {
        updateDateRangeInfo();
    } else if (mode === 'preset') {
        updatePresetRangeInfo(document.getElementById('presetRange').value);
    }
}

// 更新天数模式信息
function updateDaysInfo() {
    const days = parseInt(document.getElementById('trainingDays').value) || 0;
    const endDate = document.getElementById('endDate').value;

    let startDate;
    if (endDate) {
        const end = new Date(endDate);
        startDate = new Date(end.getTime() - days * 24 * 60 * 60 * 1000);
    } else {
        const today = new Date();
        startDate = new Date(today.getTime() - days * 24 * 60 * 60 * 1000);
    }

    const dataPoints = estimateDataPoints(days, document.getElementById('timeframe').value);

    // 这里可以添加信息显示，比如在某个元素中显示计算出的日期范围
    console.log(`训练数据范围: ${startDate.toLocaleDateString()} - ${endDate || '最新数据'}`);
    console.log(`预估数据点数: ${dataPoints}`);
}

// 更新日期范围信息
function updateDateRangeInfo() {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDateRange').value;
    const infoElement = document.getElementById('dateRangeInfo');

    if (startDate && endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);

        if (start > end) {
            infoElement.innerHTML = '<span class="text-danger">开始日期不能晚于结束日期</span>';
            infoElement.parentElement.className = 'alert alert-danger py-2 mb-0';
        } else {
            // 修复时间跨度计算 - 使用更准确的日期差计算方法
            const diffDays = calculateDateDifference(start, end);
            const dataPoints = estimateDataPoints(diffDays, document.getElementById('timeframe').value);

            // 添加更详细的信息显示
            const startStr = start.toLocaleDateString('zh-CN');
            const endStr = end.toLocaleDateString('zh-CN');

            infoElement.innerHTML = `
                <div>📅 ${startStr} 至 ${endStr}</div>
                <div>⏱️ 时间跨度: ${diffDays}天，预估数据点: ${dataPoints}个</div>
            `;
            infoElement.parentElement.className = 'alert alert-info py-2 mb-0';
        }
    } else {
        infoElement.innerHTML = '请选择完整的日期范围';
        infoElement.parentElement.className = 'alert alert-warning py-2 mb-0';
    }
}

// 更新预设范围信息
function updatePresetRangeInfo(preset) {
    const infoElement = document.getElementById('presetRangeInfo');

    const presetInfo = {
        '1m': { days: 30, desc: '最近30天的数据，适合短期模式学习' },
        '3m': { days: 90, desc: '最近3个月的数据，平衡短期和中期趋势' },
        '6m': { days: 180, desc: '最近6个月的数据，包含多种市场状态' },
        '1y': { days: 365, desc: '最近1年的数据，包含完整的市场周期' },
        '2y': { days: 730, desc: '最近2年的数据，更全面的市场经验' },
        '3y': { days: 1095, desc: '最近3年的数据，包含多个市场周期' },
        '5y': { days: 1825, desc: '最近5年的数据，长期趋势分析' },
        'ytd': { days: null, desc: '今年1月1日至今，当年市场特征' },
        'last_year': { days: 365, desc: '去年全年数据，完整年度周期' },
        'bull_market': { days: null, desc: '2020-2021牛市期间，上涨趋势学习' },
        'bear_market': { days: null, desc: '2022年熊市期间，下跌趋势学习' },
        'covid_period': { days: null, desc: '疫情期间高波动数据，极端市场学习' }
    };

    if (preset && presetInfo[preset]) {
        const info = presetInfo[preset];
        const timeframe = document.getElementById('timeframe').value;
        let dataPointsText = '';

        if (info.days) {
            const dataPoints = estimateDataPoints(info.days, timeframe);
            dataPointsText = `，约${dataPoints}个数据点`;
        }

        infoElement.innerHTML = `${info.desc}${dataPointsText}`;
        infoElement.parentElement.className = 'alert alert-success py-2 mb-0';
    } else {
        infoElement.innerHTML = '选择预设范围以查看详情';
        infoElement.parentElement.className = 'alert alert-secondary py-2 mb-0';
    }
}

// 计算两个日期之间的天数差（修复版本）
function calculateDateDifference(startDate, endDate) {
    // 重置时间为00:00:00以避免时区问题
    const start = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());
    const end = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());

    // 计算毫秒差并转换为天数
    const diffTime = end.getTime() - start.getTime();
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 包含结束日期

    return Math.max(0, diffDays); // 确保不返回负数
}

// 估算数据点数量
function estimateDataPoints(days, timeframe) {
    const pointsPerDay = {
        '1m': 1440,    // 1440分钟/天
        '5m': 288,     // 288个5分钟/天
        '15m': 96,     // 96个15分钟/天
        '1h': 24,      // 24小时/天
        'H1': 24,      // H1 = 1小时（MT5格式）
        '4h': 6,       // 6个4小时/天
        'H4': 6,       // H4 = 4小时（MT5格式）
        '1d': 1,       // 1天/天
        'D1': 1        // D1 = 1天（MT5格式）
    };

    const multiplier = pointsPerDay[timeframe] || 24;

    // 考虑交易日历：外汇市场周一到周五交易
    let tradingDayRatio = 0.7; // 默认70%（考虑周末）

    if (timeframe === '1d' || timeframe === 'D1') {
        tradingDayRatio = 5/7; // 日线图：一周5个交易日
    } else {
        tradingDayRatio = 5/7; // 其他时间框架也考虑周末
    }

    return Math.round(days * multiplier * tradingDayRatio);
}

// 获取训练数据配置
function getTrainingDataConfig() {
    const mode = document.querySelector('input[name="dateMode"]:checked').value;

    let config = {
        mode: mode,
        symbol: document.getElementById('symbol').value,
        timeframe: document.getElementById('timeframe').value
    };

    if (mode === 'days') {
        config.training_days = parseInt(document.getElementById('trainingDays').value);
        config.end_date = document.getElementById('endDate').value || null;
    } else if (mode === 'range') {
        config.start_date = document.getElementById('startDate').value;
        config.end_date = document.getElementById('endDateRange').value;
    } else if (mode === 'preset') {
        config.preset_range = document.getElementById('presetRange').value;
        config.data_quality = document.getElementById('dataQuality').value;
    }

    return config;
}

// 检查GPU状态
async function checkGPUStatus() {
    try {
        const response = await fetch('/api/deep-learning/gpu-status');
        const data = await response.json();

        const gpuStatusElement = document.getElementById('gpuStatus');

        if (data.success && data.gpu_status && data.gpu_status.gpu_available) {
            const memoryUsed = data.gpu_status.memory_used || 0;
            const memoryTotal = data.gpu_status.memory_total || 0;
            const memoryFree = memoryTotal - memoryUsed;
            const usagePercent = memoryTotal > 0 ? (memoryUsed / memoryTotal * 100) : 0;

            gpuStatusElement.innerHTML = `
                <div class="text-success">
                    <i class="fas fa-microchip me-2"></i>
                    <strong>${data.gpu_status.gpu_name}</strong>
                </div>
                <hr>
                <div class="row mb-2">
                    <div class="col-6">
                        <small class="text-muted">总内存:</small><br>
                        <strong>${memoryTotal.toFixed(1)}GB</strong>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">可用内存:</small><br>
                        <strong>${memoryFree.toFixed(1)}GB</strong>
                    </div>
                </div>
                <div class="mb-2">
                    <small class="text-muted">内存使用率:</small>
                    <div class="progress mt-1" style="height: 8px;">
                        <div class="progress-bar ${usagePercent > 80 ? 'bg-danger' : usagePercent > 60 ? 'bg-warning' : 'bg-success'}"
                             style="width: ${usagePercent}%"></div>
                    </div>
                    <small class="text-muted">${usagePercent.toFixed(1)}% (${memoryUsed.toFixed(2)}GB / ${memoryTotal.toFixed(1)}GB)</small>
                </div>
                <div class="row">
                    <div class="col-12">
                        <small class="text-muted">CUDA版本: ${data.gpu_status.cuda_version || 'N/A'}</small>
                    </div>
                </div>
            `;
        } else {
            gpuStatusElement.innerHTML = `
                <div class="text-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>GPU不可用</strong>
                </div>
                <p class="text-muted mt-2">将使用CPU进行训练</p>
                ${data.status && data.status.error ? `<small class="text-danger">错误: ${data.status.error}</small>` : ''}
            `;
            document.getElementById('useGPU').checked = false;
            document.getElementById('useGPU').disabled = true;
        }
    } catch (error) {
        console.error('检查GPU状态失败:', error);
        const gpuStatusElement = document.getElementById('gpuStatus');
        gpuStatusElement.innerHTML = `
            <div class="text-danger">
                <i class="fas fa-times-circle me-2"></i>
                <strong>检查GPU状态失败</strong>
            </div>
            <p class="text-muted mt-2">请检查网络连接</p>
        `;
    }
}

// 提交训练表单
document.getElementById('trainingForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    // 获取训练数据配置
    const dataConfig = getTrainingDataConfig();

    const formData = {
        model_name: document.getElementById('modelName').value,
        model_type: document.getElementById('modelType').value,
        symbol: document.getElementById('symbol').value,
        timeframe: document.getElementById('timeframe').value,

        // 新的日期配置
        data_config: dataConfig,

        // 保留向后兼容性
        training_days: dataConfig.mode === 'days' ? dataConfig.training_days : null,

        sequence_length: parseInt(document.getElementById('sequenceLength').value),
        hidden_size: parseInt(document.getElementById('hiddenSize').value),
        num_layers: parseInt(document.getElementById('numLayers').value),
        dropout: parseFloat(document.getElementById('dropout').value),
        batch_size: parseInt(document.getElementById('batchSize').value),
        learning_rate: parseFloat(document.getElementById('learningRate').value),
        epochs: parseInt(document.getElementById('epochs').value),
        patience: parseInt(document.getElementById('patience').value),
        early_stopping: document.getElementById('earlyStoppingEnabled').checked,
        min_epochs: parseInt(document.getElementById('minEpochs').value),
        use_gpu: document.getElementById('useGPU').checked,
        save_checkpoints: document.getElementById('saveCheckpoints').checked,
        features: {
            price: document.getElementById('usePrice').checked,
            volume: document.getElementById('useVolume').checked,
            technical: document.getElementById('useTechnical').checked,
            time: document.getElementById('useTime').checked
        }
    };
    
    try {
        const startBtn = document.getElementById('startDataPrepBtn');
        startBtn.disabled = true;
        startBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>准备中...';

        const response = await fetch('/api/deep-learning/start-data-preparation', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });

        const result = await response.json();

        if (result.success) {
            trainingTaskId = result.task_id;

            // 保存训练状态
            saveTrainingState(trainingTaskId, formData, 'data_preparation');

            showTrainingProgress();
            startProgressMonitoring();

            showAlert('数据准备任务已启动！', 'success');
        } else {
            showAlert('启动数据准备失败: ' + result.error, 'danger');
            startBtn.disabled = false;
            startBtn.innerHTML = '<i class="fas fa-database me-1"></i>开始数据准备';
        }
    } catch (error) {
        console.error('启动数据准备失败:', error);
        showAlert('启动数据准备失败: ' + error.message, 'danger');

        const startBtn = document.getElementById('startDataPrepBtn');
        startBtn.disabled = false;
        startBtn.innerHTML = '<i class="fas fa-database me-1"></i>开始数据准备';
    }
});

// 模型训练按钮处理
document.getElementById('startModelTrainingBtn').addEventListener('click', async function(e) {
    e.preventDefault();

    if (!trainingTaskId) {
        showAlert('请先完成数据准备！', 'warning');
        return;
    }

    // 🔧 修复：在启动训练前先检查任务状态
    console.log('🔍 检查任务状态...');
    const statusCheck = await checkTaskStatusBeforeTraining(trainingTaskId);
    if (!statusCheck.canTrain) {
        showAlert(statusCheck.message, 'warning');
        return;
    }

    const startBtn = document.getElementById('startModelTrainingBtn');
    startBtn.disabled = true;
    startBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>启动中...';

    try {
        console.log('🚀 启动模型训练，任务ID:', trainingTaskId);

        const response = await fetch(`/api/deep-learning/start-model-training/${trainingTaskId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();
        console.log('📋 模型训练启动结果:', result);

        if (result.success) {
            // 隐藏模型训练按钮
            startBtn.style.display = 'none';

            showAlert('模型训练已启动！', 'success');
        } else {
            showAlert('启动模型训练失败: ' + result.error, 'danger');
            startBtn.disabled = false;
            startBtn.innerHTML = '<i class="fas fa-play me-1"></i>开始模型训练';
        }
    } catch (error) {
        console.error('启动模型训练失败:', error);
        showAlert('启动模型训练失败: ' + error.message, 'danger');

        const startBtn = document.getElementById('startModelTrainingBtn');
        startBtn.disabled = false;
        startBtn.innerHTML = '<i class="fas fa-play me-1"></i>开始模型训练';
    }
});

// 显示训练进度
function showTrainingProgress() {
    document.getElementById('trainingProgressCard').style.display = 'block';
    document.getElementById('trainingControls').style.display = 'block';
}

// 开始进度监控
function startProgressMonitoring() {
    if (progressInterval) {
        clearInterval(progressInterval);
    }

    console.log('🔄 开始训练进度监控...');

    // 立即执行一次
    updateTrainingProgress();

    // 设置定时器，每1秒更新一次
    progressInterval = setInterval(async () => {
        if (trainingTaskId) {
            await updateTrainingProgress();
        } else {
            console.warn('⚠️ 训练任务ID为空，停止进度监控');
            clearInterval(progressInterval);
        }
    }, 1000); // 改为1秒更新一次，提高响应性

    // 开始GPU状态监控
    startGPUStatusMonitoring();
}

// 更新训练进度
async function updateTrainingProgress() {
    try {
        if (!trainingTaskId) {
            console.warn('⚠️ 训练任务ID为空');
            return;
        }

        console.log(`🔄 更新训练进度: ${trainingTaskId}`);

        // 获取训练进度
        const progressResponse = await fetch(`/api/deep-learning/training-progress/${trainingTaskId}`);

        if (!progressResponse.ok) {
            throw new Error(`HTTP ${progressResponse.status}: ${progressResponse.statusText}`);
        }

        const progressData = await progressResponse.json();
        console.log('📊 进度数据:', progressData);

        // 获取控制状态
        let controlData = { success: false, control: {} };
        try {
            const controlResponse = await fetch(`/api/deep-learning/training/${trainingTaskId}/control-status`);
            if (controlResponse.ok) {
                controlData = await controlResponse.json();
            }
        } catch (controlError) {
            console.warn('⚠️ 获取控制状态失败:', controlError);
        }

        if (progressData.success) {
            const progress = progressData.progress;
            const control = controlData.success ? controlData.control : {};

            console.log('📈 进度信息:', progress);

            // 更新控制按钮状态
            if (control.can_control) {
                if (control.is_paused) {
                    document.getElementById('pauseTrainingBtn').style.display = 'none';
                    document.getElementById('resumeTrainingBtn').style.display = 'inline-block';
                } else {
                    document.getElementById('pauseTrainingBtn').style.display = 'inline-block';
                    document.getElementById('resumeTrainingBtn').style.display = 'none';
                }
            }

            // 状态显示
            let statusText = progress.status;
            if (control.is_paused) {
                statusText += ' (已暂停)';
            }

            const progressElement = document.getElementById('trainingProgress');

            // 解析日志信息获取详细状态
            let detailsHtml = '';
            let stageInfo = '';

            if (progress.logs) {
                try {
                    const logs = JSON.parse(progress.logs);

                    // 根据不同阶段显示不同信息
                    switch(logs.stage) {
                        case 'data_preparation':
                            stageInfo = `📊 ${logs.message}`;
                            detailsHtml = `
                                <div class="alert alert-info py-2 mb-2">
                                    <i class="fas fa-database me-2"></i>正在准备训练数据...
                                </div>
                            `;
                            break;

                        case 'data_fetching':
                            stageInfo = `🔗 ${logs.message}`;
                            detailsHtml = `
                                <div class="alert alert-info py-2 mb-2">
                                    <i class="fas fa-download me-2"></i>正在获取历史数据
                                    <div class="mt-1">
                                        <small>品种: ${logs.symbol} | 时间框架: ${logs.timeframe} | 天数: ${logs.days}</small>
                                    </div>
                                </div>
                            `;
                            break;

                        case 'data_fetched':
                            stageInfo = `✅ ${logs.message}`;
                            detailsHtml = `
                                <div class="alert alert-success py-2 mb-2">
                                    <i class="fas fa-check me-2"></i>数据获取完成
                                    <div class="mt-1">
                                        <small>数据点: ${logs.data_points} | 时间范围: ${logs.data_range?.start} 至 ${logs.data_range?.end}</small>
                                    </div>
                                </div>
                            `;
                            break;

                        case 'feature_calculation':
                            stageInfo = `📈 ${logs.message}`;
                            detailsHtml = `
                                <div class="alert alert-info py-2 mb-2">
                                    <i class="fas fa-calculator me-2"></i>正在计算技术指标
                                    <div class="mt-1">
                                        <small>特征: ${logs.features?.join(', ')}</small>
                                    </div>
                                </div>
                            `;
                            break;

                        case 'sequence_creation':
                            stageInfo = `🔢 ${logs.message}`;
                            detailsHtml = `
                                <div class="alert alert-info py-2 mb-2">
                                    <i class="fas fa-list-ol me-2"></i>正在创建训练序列
                                    <div class="mt-1">
                                        <small>序列长度: ${logs.sequence_length} | 特征维度: ${logs.feature_shape?.join('×')}</small>
                                    </div>
                                </div>
                            `;
                            break;

                        case 'data_splitting':
                            stageInfo = `✂️ ${logs.message}`;
                            detailsHtml = `
                                <div class="alert alert-info py-2 mb-2">
                                    <i class="fas fa-cut me-2"></i>正在分割数据集
                                    <div class="mt-1">
                                        <small>总序列: ${logs.total_sequences} | 验证比例: ${(logs.validation_split * 100).toFixed(0)}%</small>
                                    </div>
                                </div>
                            `;
                            break;

                        case 'data_ready':
                            stageInfo = `✅ ${logs.message}`;

                            // 🔧 改进：显示更详细的数据准备信息
                            const dataInfo = logs.data_info || {};
                            const trainShape = dataInfo.X_train_shape || logs.train_shape || [];
                            const valShape = dataInfo.X_val_shape || logs.val_shape || [];
                            const trainSamples = logs.train_samples || (trainShape[0] || 'N/A');
                            const valSamples = logs.val_samples || (valShape[0] || 'N/A');
                            const featureCount = logs.feature_count || (trainShape[trainShape.length - 1] || 'N/A');

                            detailsHtml = `
                                <div class="alert alert-success py-3 mb-2">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-2">
                                                <i class="fas fa-check-circle me-2"></i>数据准备完成！
                                            </h6>
                                            <div class="row">
                                                <div class="col-6">
                                                    <small class="text-muted">训练集:</small><br>
                                                    <strong>${trainSamples} 样本</strong><br>
                                                    <small>形状: ${trainShape.join('×')}</small>
                                                </div>
                                                <div class="col-6">
                                                    <small class="text-muted">验证集:</small><br>
                                                    <strong>${valSamples} 样本</strong><br>
                                                    <small>特征数: ${featureCount}</small>
                                                </div>
                                            </div>
                                            ${logs.cache_saved ? '<div class="mt-2"><small class="text-success"><i class="fas fa-save me-1"></i>数据已缓存</small></div>' : ''}
                                        </div>
                                        <div>
                                            <button type="button" class="btn btn-success btn-sm" onclick="showModelTrainingButton()">
                                                <i class="fas fa-play me-1"></i>开始训练
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            `;
                            break;

                        case 'model_training':
                            stageInfo = `🚀 ${logs.message}`;
                            detailsHtml = `
                                <div class="alert alert-primary py-2 mb-2">
                                    <i class="fas fa-brain me-2"></i>模型训练进行中
                                    <div class="mt-1">
                                        <small>神经网络正在学习数据模式，请耐心等待...</small>
                                    </div>
                                </div>
                            `;
                            break;

                        default:
                            if (logs.message) {
                                stageInfo = logs.message;
                            }
                    }
                } catch (e) {
                    console.warn('解析日志失败:', e);
                }
            }

            progressElement.innerHTML = `
                ${detailsHtml}
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>总体进度</span>
                        <span>${progress.progress || 0}%</span>
                    </div>
                    <div class="progress mb-2">
                        <div class="progress-bar ${control.is_paused ? 'bg-warning' : ''}" style="width: ${progress.progress || 0}%"></div>
                    </div>
                    ${stageInfo ? `<small class="text-muted">${stageInfo}</small>` : ''}
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>训练轮次</span>
                        <span>${progress.epoch || 0}/${progress.total_epochs || 0}</span>
                    </div>
                    ${progress.total_epochs > 0 ? `
                        <div class="progress">
                            <div class="progress-bar bg-info" style="width: ${((progress.epoch || 0) / progress.total_epochs * 100)}%"></div>
                        </div>
                    ` : ''}
                </div>

                <div class="row">
                    <div class="col-6">
                        <small class="text-muted">训练损失:</small><br>
                        <strong>${progress.train_loss?.toFixed(4) || 'N/A'}</strong>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">验证损失:</small><br>
                        <strong>${progress.val_loss?.toFixed(4) || 'N/A'}</strong>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-muted">状态: ${statusText}</small>
                </div>
            `;

            // 检查数据准备完成状态
            if (progress.status === 'data_ready') {
                // 显示模型训练按钮
                showModelTrainingButton();

                // 更新数据准备按钮状态
                const dataPrepBtn = document.getElementById('startDataPrepBtn');
                dataPrepBtn.disabled = true;
                dataPrepBtn.innerHTML = '<i class="fas fa-check me-1"></i>数据准备完成';
                dataPrepBtn.classList.remove('btn-primary');
                dataPrepBtn.classList.add('btn-success');

                showDataReadyDialog();
            }

            if (progress.status === 'completed' || progress.status === 'failed' || progress.status === 'stopped') {
                clearInterval(progressInterval);
                stopGPUStatusMonitoring();

                // 隐藏控制按钮
                document.getElementById('trainingControls').style.display = 'none';

                // 重置按钮状态
                resetButtonStates();

                // 更新并清除训练状态
                saveTrainingState(trainingTaskId, null, progress.status);
                setTimeout(() => {
                    clearTrainingState();
                }, 2000); // 2秒后清除状态，给用户时间看到完成信息

                trainingTaskId = null;

                if (progress.status === 'completed') {
                    showAlert('模型训练完成！', 'success');
                } else if (progress.status === 'stopped') {
                    showAlert('模型训练已停止！', 'warning');
                } else {
                    showAlert('模型训练失败！', 'danger');
                }
            }
        } else {
            console.error('❌ 获取进度失败:', progressData.error);

            // 显示错误信息
            const progressElement = document.getElementById('trainingProgress');
            progressElement.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    获取训练进度失败: ${progressData.error}
                </div>
            `;
        }
    } catch (error) {
        console.error('❌ 获取训练进度异常:', error);

        // 显示网络错误
        const progressElement = document.getElementById('trainingProgress');
        if (progressElement) {
            progressElement.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-wifi me-2"></i>
                    网络连接异常，正在重试...
                </div>
            `;
        }

        // 如果是网络错误，不要停止监控，继续重试
        if (error.name === 'TypeError' || error.message.includes('fetch')) {
            console.log('🔄 网络错误，将继续重试...');
        }
    }
}

// 开始GPU状态监控
function startGPUStatusMonitoring() {
    if (gpuStatusInterval) {
        clearInterval(gpuStatusInterval);
    }

    console.log('🎮 开始GPU状态监控...');

    // 每5秒更新一次GPU状态
    gpuStatusInterval = setInterval(async () => {
        await checkGPUStatus();
    }, 5000);
}

// 停止GPU状态监控
function stopGPUStatusMonitoring() {
    if (gpuStatusInterval) {
        clearInterval(gpuStatusInterval);
        gpuStatusInterval = null;
        console.log('🛑 GPU状态监控已停止');
    }
}

// 加载预设配置
function loadPreset(presetType = 'balanced') {
    const presets = {
        quick: {
            sequenceLength: 30,
            hiddenSize: 64,
            numLayers: 1,
            batchSize: 64,
            epochs: 50,
            learningRate: 0.01,
            patience: 15,
            minEpochs: 10
        },
        balanced: {
            sequenceLength: 60,
            hiddenSize: 128,
            numLayers: 2,
            batchSize: 32,
            epochs: 100,
            learningRate: 0.001,
            patience: 20,
            minEpochs: 20
        },
        high_accuracy: {
            sequenceLength: 120,
            hiddenSize: 256,
            numLayers: 3,
            batchSize: 16,
            epochs: 200,
            learningRate: 0.0005,
            patience: 30,
            minEpochs: 30
        },
        experimental: {
            sequenceLength: 100,
            hiddenSize: 512,
            numLayers: 4,
            batchSize: 8,
            epochs: 300,
            learningRate: 0.0001,
            patience: 40,
            minEpochs: 50
        }
    };
    
    const preset = presets[presetType];
    if (preset) {
        Object.keys(preset).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                element.value = preset[key];
            }
        });
        
        showAlert(`已加载${presetType}预设配置`, 'info');
    }
}

// 重置表单
function resetForm() {
    document.getElementById('trainingForm').reset();
    document.getElementById('trainingProgressCard').style.display = 'none';
    document.getElementById('trainingControls').style.display = 'none';

    if (progressInterval) {
        clearInterval(progressInterval);
    }

    stopGPUStatusMonitoring();

    // 清除训练状态
    clearTrainingState();
    trainingTaskId = null;

    // 重置按钮状态
    resetButtonStates();

    // 重新初始化日期选择器
    initializeDateRangeSelector();
}

// 暂停训练
async function pauseTraining() {
    if (!trainingTaskId) return;

    try {
        const response = await fetch(`/api/deep-learning/training/${trainingTaskId}/pause`, {
            method: 'POST'
        });

        const result = await response.json();

        if (result.success) {
            document.getElementById('pauseTrainingBtn').style.display = 'none';
            document.getElementById('resumeTrainingBtn').style.display = 'inline-block';
            showAlert('训练已暂停', 'info');
        } else {
            showAlert('暂停训练失败: ' + result.error, 'danger');
        }
    } catch (error) {
        console.error('暂停训练失败:', error);
        showAlert('暂停训练失败: ' + error.message, 'danger');
    }
}

// 恢复训练
async function resumeTraining() {
    if (!trainingTaskId) return;

    try {
        const response = await fetch(`/api/deep-learning/training/${trainingTaskId}/resume`, {
            method: 'POST'
        });

        const result = await response.json();

        if (result.success) {
            document.getElementById('pauseTrainingBtn').style.display = 'inline-block';
            document.getElementById('resumeTrainingBtn').style.display = 'none';
            showAlert('训练已恢复', 'info');
        } else {
            showAlert('恢复训练失败: ' + result.error, 'danger');
        }
    } catch (error) {
        console.error('恢复训练失败:', error);
        showAlert('恢复训练失败: ' + error.message, 'danger');
    }
}

// 停止训练
async function stopTraining() {
    if (!trainingTaskId) return;

    if (!confirm('确定要停止训练吗？停止后无法恢复。')) {
        return;
    }

    try {
        const response = await fetch(`/api/deep-learning/training/${trainingTaskId}/stop`, {
            method: 'POST'
        });

        const result = await response.json();

        if (result.success) {
            // 停止进度监控
            if (progressInterval) {
                clearInterval(progressInterval);
            }

            stopGPUStatusMonitoring();

            // 隐藏控制按钮
            document.getElementById('trainingControls').style.display = 'none';

            // 重置开始按钮
            const startBtn = document.getElementById('startTrainingBtn');
            startBtn.disabled = false;
            startBtn.innerHTML = '<i class="fas fa-play me-1"></i>开始训练';

            // 清除训练状态
            clearTrainingState();
            trainingTaskId = null;

            showAlert('训练已停止', 'warning');
        } else {
            showAlert('停止训练失败: ' + result.error, 'danger');
        }
    } catch (error) {
        console.error('停止训练失败:', error);
        showAlert('停止训练失败: ' + error.message, 'danger');
    }
}

// 显示提示信息
function showAlert(message, type = 'info') {
    // 移除现有的alert
    const existingAlert = document.querySelector('.custom-alert');
    if (existingAlert) {
        existingAlert.remove();
    }

    // 创建自定义alert
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} custom-alert`;
    alertDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        min-width: 300px;
        max-width: 500px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease-in-out;
    `;

    // 根据类型设置图标
    let icon = '';
    switch(type) {
        case 'success':
            icon = '<i class="fas fa-check-circle me-2"></i>';
            break;
        case 'danger':
            icon = '<i class="fas fa-exclamation-triangle me-2"></i>';
            break;
        case 'warning':
            icon = '<i class="fas fa-exclamation-circle me-2"></i>';
            break;
        default:
            icon = '<i class="fas fa-info-circle me-2"></i>';
    }

    alertDiv.innerHTML = `
        <div class="d-flex align-items-center">
            ${icon}
            <span class="flex-grow-1">${message}</span>
            <button type="button" class="btn-close ms-2" onclick="closeCustomAlert(this)"></button>
        </div>
    `;

    // 添加到页面
    document.body.appendChild(alertDiv);

    // 显示动画
    setTimeout(() => {
        alertDiv.style.opacity = '1';
        alertDiv.style.transform = 'translateX(0)';
    }, 10);

    // 自动关闭
    const autoCloseTime = type === 'success' ? 4000 : 6000;
    setTimeout(() => {
        closeCustomAlert(alertDiv.querySelector('.btn-close'));
    }, autoCloseTime);
}

// 关闭自定义alert
function closeCustomAlert(closeBtn) {
    const alertDiv = closeBtn.closest('.custom-alert');
    if (alertDiv) {
        alertDiv.style.opacity = '0';
        alertDiv.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (alertDiv && alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 300);
    }
}

// 显示模型类型说明
function showModelTypeDescription() {
    const modelType = document.getElementById('modelType').value;
    const descriptionDiv = document.getElementById('modelTypeDescription');
    const nameSpan = document.getElementById('modelTypeName');
    const contentDiv = document.getElementById('modelTypeContent');

    if (!modelType) {
        descriptionDiv.style.display = 'none';
        return;
    }

    const modelDescriptions = {
        'lstm': {
            name: 'LSTM (长短期记忆网络)',
            description: `
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-success"><i class="fas fa-check-circle"></i> 优点</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-arrow-right text-primary"></i> 擅长处理时间序列数据</li>
                            <li><i class="fas fa-arrow-right text-primary"></i> 能够记住长期依赖关系</li>
                            <li><i class="fas fa-arrow-right text-primary"></i> 解决了传统RNN的梯度消失问题</li>
                            <li><i class="fas fa-arrow-right text-primary"></i> 在金融预测中表现稳定</li>
                            <li><i class="fas fa-arrow-right text-primary"></i> 训练相对稳定，收敛性好</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-warning"><i class="fas fa-exclamation-triangle"></i> 缺点</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-arrow-right text-secondary"></i> 训练速度相对较慢</li>
                            <li><i class="fas fa-arrow-right text-secondary"></i> 参数较多，容易过拟合</li>
                            <li><i class="fas fa-arrow-right text-secondary"></i> 对超参数敏感</li>
                        </ul>
                        <div class="mt-3">
                            <h6 class="text-info"><i class="fas fa-lightbulb"></i> 适用场景</h6>
                            <p class="small text-muted">适合中长期价格预测，特别是需要考虑历史趋势的场景。推荐用于日线和小时线数据。</p>
                        </div>
                    </div>
                </div>
            `
        },
        'gru': {
            name: 'GRU (门控循环单元)',
            description: `
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-success"><i class="fas fa-check-circle"></i> 优点</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-arrow-right text-primary"></i> 比LSTM结构更简单</li>
                            <li><i class="fas fa-arrow-right text-primary"></i> 训练速度更快</li>
                            <li><i class="fas fa-arrow-right text-primary"></i> 参数更少，不易过拟合</li>
                            <li><i class="fas fa-arrow-right text-primary"></i> 在短序列上表现优秀</li>
                            <li><i class="fas fa-arrow-right text-primary"></i> 内存占用更少</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-warning"><i class="fas fa-exclamation-triangle"></i> 缺点</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-arrow-right text-secondary"></i> 长期记忆能力略弱于LSTM</li>
                            <li><i class="fas fa-arrow-right text-secondary"></i> 在复杂模式识别上可能不如LSTM</li>
                        </ul>
                        <div class="mt-3">
                            <h6 class="text-info"><i class="fas fa-lightbulb"></i> 适用场景</h6>
                            <p class="small text-muted">适合快速训练和短期预测，特别是分钟级数据。在计算资源有限时是很好的选择。</p>
                        </div>
                    </div>
                </div>
            `
        },
        'transformer': {
            name: 'Transformer',
            description: `
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-success"><i class="fas fa-check-circle"></i> 优点</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-arrow-right text-primary"></i> 并行计算能力强</li>
                            <li><i class="fas fa-arrow-right text-primary"></i> 注意力机制捕获复杂关系</li>
                            <li><i class="fas fa-arrow-right text-primary"></i> 在长序列上表现优异</li>
                            <li><i class="fas fa-arrow-right text-primary"></i> 可解释性较好</li>
                            <li><i class="fas fa-arrow-right text-primary"></i> 最新的深度学习架构</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-warning"><i class="fas fa-exclamation-triangle"></i> 缺点</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-arrow-right text-secondary"></i> 需要大量数据才能训练好</li>
                            <li><i class="fas fa-arrow-right text-secondary"></i> 计算资源需求高</li>
                            <li><i class="fas fa-arrow-right text-secondary"></i> 超参数调优复杂</li>
                            <li><i class="fas fa-arrow-right text-secondary"></i> 在小数据集上可能过拟合</li>
                        </ul>
                        <div class="mt-3">
                            <h6 class="text-info"><i class="fas fa-lightbulb"></i> 适用场景</h6>
                            <p class="small text-muted">适合大数据量的复杂模式识别，特别是多因子分析。需要强大的GPU支持。</p>
                        </div>
                    </div>
                </div>
            `
        },
        'cnn_lstm': {
            name: 'CNN-LSTM混合模型',
            description: `
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-success"><i class="fas fa-check-circle"></i> 优点</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-arrow-right text-primary"></i> 结合CNN的特征提取能力</li>
                            <li><i class="fas fa-arrow-right text-primary"></i> LSTM处理时序依赖</li>
                            <li><i class="fas fa-arrow-right text-primary"></i> 能识别局部模式和全局趋势</li>
                            <li><i class="fas fa-arrow-right text-primary"></i> 在技术分析中表现优秀</li>
                            <li><i class="fas fa-arrow-right text-primary"></i> 适合多维特征数据</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-warning"><i class="fas fa-exclamation-triangle"></i> 缺点</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-arrow-right text-secondary"></i> 模型复杂度高</li>
                            <li><i class="fas fa-arrow-right text-secondary"></i> 训练时间长</li>
                            <li><i class="fas fa-arrow-right text-secondary"></i> 需要更多的调参经验</li>
                            <li><i class="fas fa-arrow-right text-secondary"></i> 容易过拟合</li>
                        </ul>
                        <div class="mt-3">
                            <h6 class="text-info"><i class="fas fa-lightbulb"></i> 适用场景</h6>
                            <p class="small text-muted">适合包含技术指标的多维数据预测，特别是需要同时考虑局部模式和时序关系的场景。</p>
                        </div>
                    </div>
                </div>
            `
        },
        'attention_lstm': {
            name: '注意力LSTM',
            description: `
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-success"><i class="fas fa-check-circle"></i> 优点</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-arrow-right text-primary"></i> 注意力机制提高预测精度</li>
                            <li><i class="fas fa-arrow-right text-primary"></i> 能够关注重要的时间点</li>
                            <li><i class="fas fa-arrow-right text-primary"></i> 可解释性较好</li>
                            <li><i class="fas fa-arrow-right text-primary"></i> 在长序列上表现优异</li>
                            <li><i class="fas fa-arrow-right text-primary"></i> 能够处理不规律的时间模式</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-warning"><i class="fas fa-exclamation-triangle"></i> 缺点</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-arrow-right text-secondary"></i> 计算复杂度较高</li>
                            <li><i class="fas fa-arrow-right text-secondary"></i> 需要更多的训练数据</li>
                            <li><i class="fas fa-arrow-right text-secondary"></i> 超参数调优复杂</li>
                        </ul>
                        <div class="mt-3">
                            <h6 class="text-info"><i class="fas fa-lightbulb"></i> 适用场景</h6>
                            <p class="small text-muted">适合需要识别关键时间点的预测任务，如突发事件对价格的影响分析。</p>
                        </div>
                    </div>
                </div>
            `
        }
    };
    const modelInfo = modelDescriptions[modelType];
    if (modelInfo) {
        nameSpan.textContent = modelInfo.name;
        contentDiv.innerHTML = modelInfo.description;
        descriptionDiv.style.display = 'block';

        // 添加淡入动画
        descriptionDiv.style.opacity = '0';
        setTimeout(() => {
            descriptionDiv.style.transition = 'opacity 0.3s ease-in-out';
            descriptionDiv.style.opacity = '1';
        }, 10);
    } else {
        descriptionDiv.style.display = 'none';
    }
}

// 显示数据准备完成对话框
function showDataReadyDialog() {
    // 使用简单的确认对话框，避免复杂的模态框问题
    const result = confirm('🎉 数据准备完成！\n\n历史数据已获取并处理完成，技术指标已计算，训练序列已创建。\n\n是否立即开始模型训练？\n\n点击"确定"立即开始训练\n点击"取消"稍后手动开始');

    if (result) {
        // 用户选择立即开始训练
        startTrainingFromDialog();
    } else {
        // 用户选择稍后训练，显示成功提示
        showAlert('数据准备完成！您可以稍后点击"开始模型训练"按钮。', 'success');
    }
}

// 从对话框开始训练
function startTrainingFromDialog() {
    // 直接触发模型训练按钮点击
    const trainBtn = document.getElementById('startModelTrainingBtn');
    if (trainBtn && trainBtn.style.display !== 'none') {
        trainBtn.click();
    } else {
        // 如果按钮不可见，先显示它
        showModelTrainingButton();
        setTimeout(() => {
            const btn = document.getElementById('startModelTrainingBtn');
            if (btn) {
                btn.click();
            }
        }, 100);
    }
}

// 🔧 新增：检查任务状态是否可以开始训练
async function checkTaskStatusBeforeTraining(taskId) {
    try {
        const response = await fetch(`/api/deep-learning/training-progress/${taskId}`);
        const result = await response.json();

        if (!result.success) {
            return {
                canTrain: false,
                message: '无法获取任务状态，请刷新页面重试'
            };
        }

        const progress = result.progress;
        console.log('📊 当前任务状态:', progress);

        // 显示详细的状态信息
        showTaskStatusDetails(progress);

        if (progress.status === 'data_ready') {
            return {
                canTrain: true,
                message: '数据准备完成，可以开始训练'
            };
        } else if (progress.status === 'running') {
            // 检查具体的运行阶段
            const logs = progress.logs;
            if (logs && typeof logs === 'string') {
                try {
                    const logData = JSON.parse(logs);

                    if (logData.stage === 'data_ready') {
                        return {
                            canTrain: false,
                            message: '数据准备已完成，但状态同步中，请稍等片刻后重试'
                        };
                    } else if (logData.stage === 'model_training') {
                        return {
                            canTrain: false,
                            message: '模型训练已在进行中，请等待训练完成'
                        };
                    } else if (logData.stage === 'data_preparation') {
                        return {
                            canTrain: false,
                            message: '数据准备进行中，请等待数据准备完成'
                        };
                    }
                } catch (e) {
                    console.warn('日志解析失败:', e);
                }
            }

            return {
                canTrain: false,
                message: `任务正在运行中（${progress.status}），请等待当前阶段完成`
            };
        } else {
            return {
                canTrain: false,
                message: `任务状态不正确（${progress.status}），请先完成数据准备`
            };
        }

    } catch (error) {
        console.error('检查任务状态失败:', error);
        return {
            canTrain: false,
            message: '检查任务状态失败，请检查网络连接'
        };
    }
}

// 🔧 新增：显示任务状态详情
function showTaskStatusDetails(progress) {
    const statusInfo = `
        📊 任务状态详情:
        • 状态: ${progress.status}
        • 进度: ${progress.progress}%
        • 模型ID: ${progress.model_id}
        • 更新时间: ${progress.updated_at}
    `;
    console.log(statusInfo);

    // 在页面上显示状态信息
    const statusElement = document.getElementById('taskStatusInfo');
    if (statusElement) {
        statusElement.innerHTML = `
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle me-2"></i>任务状态</h6>
                <div class="row">
                    <div class="col-6">
                        <small class="text-muted">状态:</small><br>
                        <strong>${progress.status}</strong>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">进度:</small><br>
                        <strong>${progress.progress}%</strong>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-muted">最后更新: ${new Date(progress.updated_at).toLocaleString()}</small>
                </div>
            </div>
        `;
    }
}

// 显示模型训练按钮
function showModelTrainingButton() {
    const modelTrainingBtn = document.getElementById('startModelTrainingBtn');
    modelTrainingBtn.style.display = 'inline-block';
    modelTrainingBtn.disabled = false;
    modelTrainingBtn.innerHTML = '<i class="fas fa-play me-1"></i>开始模型训练';
}

// 🔧 新增：手动检查当前任务状态
async function checkCurrentTaskStatus() {
    if (!trainingTaskId) {
        showAlert('没有活跃的训练任务', 'info');
        return;
    }

    console.log('🔍 手动检查任务状态...');
    const statusCheck = await checkTaskStatusBeforeTraining(trainingTaskId);

    // 显示状态信息
    const statusElement = document.getElementById('taskStatusInfo');
    statusElement.style.display = 'block';

    // 根据状态更新按钮
    if (statusCheck.canTrain) {
        showModelTrainingButton();
        showAlert('✅ ' + statusCheck.message, 'success');
    } else {
        showAlert('⚠️ ' + statusCheck.message, 'warning');
    }
}

// 重置按钮状态
function resetButtonStates() {
    // 重置数据准备按钮
    const dataPrepBtn = document.getElementById('startDataPrepBtn');
    dataPrepBtn.disabled = false;
    dataPrepBtn.innerHTML = '<i class="fas fa-database me-1"></i>开始数据准备';
    dataPrepBtn.classList.remove('btn-success');
    dataPrepBtn.classList.add('btn-primary');

    // 隐藏模型训练按钮
    const modelTrainingBtn = document.getElementById('startModelTrainingBtn');
    modelTrainingBtn.style.display = 'none';
    modelTrainingBtn.disabled = false;
    modelTrainingBtn.innerHTML = '<i class="fas fa-play me-1"></i>开始模型训练';

    // 隐藏状态信息
    const statusElement = document.getElementById('taskStatusInfo');
    statusElement.style.display = 'none';
}
</script>

<style>
/* 日期范围选择器样式 */
.date-mode-content {
    transition: all 0.3s ease;
}

.btn-group .btn-check:checked + .btn {
    background-color: #4e73df;
    border-color: #4e73df;
    color: white;
}

.btn-group .btn-outline-primary {
    border-color: #4e73df;
    color: #4e73df;
}

.btn-group .btn-outline-primary:hover {
    background-color: #4e73df;
    border-color: #4e73df;
    color: white;
}

/* 日期输入框样式 */
input[type="date"] {
    position: relative;
}

input[type="date"]::-webkit-calendar-picker-indicator {
    background: transparent;
    bottom: 0;
    color: transparent;
    cursor: pointer;
    height: auto;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    width: auto;
}

/* 信息提示框样式 */
.alert {
    border-radius: 0.375rem;
    font-size: 0.875rem;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-secondary {
    background-color: #e2e3e5;
    border-color: #d6d8db;
    color: #383d41;
}

/* 卡片样式 */
.card.border-light {
    border: 1px solid #e3e6f0;
}

.card.bg-light {
    background-color: #f8f9fc;
}

/* 表单标签样式 */
.form-label {
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 0.5rem;
}

/* 按钮组样式 */
.btn-group {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        border-radius: 0.375rem !important;
        margin-bottom: 0.25rem;
    }

    .btn-group .btn:last-child {
        margin-bottom: 0;
    }
}

/* 动画效果 */
.date-mode-content {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 输入框焦点样式 */
.form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.form-select:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}
</style>
{% endblock %}
