#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终的训练进度修复
"""

import requests
import json
import time
import sqlite3
from datetime import datetime

def login_session():
    """登录获取会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        
        if response.status_code == 200:
            print("✅ 登录成功")
            return session
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def stop_all_running_tasks(session):
    """停止所有运行中的任务"""
    print("\n🛑 停止所有运行中的任务")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id FROM training_tasks
            WHERE status = 'running'
            ORDER BY updated_at DESC
        ''')
        
        tasks = cursor.fetchall()
        conn.close()
        
        if not tasks:
            print("ℹ️ 没有运行中的任务")
            return True
        
        print(f"📋 找到 {len(tasks)} 个运行中的任务")
        
        stopped_count = 0
        for task in tasks:
            task_id = task[0]
            print(f"🛑 停止任务: {task_id[:8]}...")
            
            try:
                response = session.post(f'http://127.0.0.1:5000/api/deep-learning/stop-training/{task_id}')
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        print(f"   ✅ 停止成功")
                        stopped_count += 1
                    else:
                        print(f"   ❌ 停止失败: {result.get('error')}")
                else:
                    print(f"   ❌ HTTP错误: {response.status_code}")
            except Exception as e:
                print(f"   ❌ 停止异常: {e}")
        
        print(f"📊 停止结果: {stopped_count}/{len(tasks)} 个任务成功停止")
        
        if stopped_count > 0:
            print("⏳ 等待状态更新...")
            time.sleep(3)
        
        return True
        
    except Exception as e:
        print(f"❌ 停止任务失败: {e}")
        return False

def start_test_training(session):
    """启动测试训练"""
    print("\n🚀 启动测试训练")
    print("=" * 40)
    
    # 使用最小化配置确保快速测试
    config = {
        'model_name': f'FINAL_TEST_{int(time.time())}',
        'model_type': 'lstm',
        'symbol': 'XAUUSD',
        'timeframe': 'H1',
        'epochs': 3,  # 只训练3轮
        'batch_size': 8,  # 小批次
        'learning_rate': 0.01,
        'sequence_length': 20,  # 适中序列
        'data_config': {
            'days': 10,  # 10天数据
            'validation_split': 0.2
        },
        'early_stopping': False,
        'save_checkpoints': False
    }
    
    print(f"📝 测试配置:")
    for key, value in config.items():
        if key != 'data_config':
            print(f"   {key}: {value}")
    
    try:
        response = session.post(
            'http://127.0.0.1:5000/api/deep-learning/start-training',
            json=config,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"✅ 训练启动成功!")
                print(f"   任务ID: {task_id}")
                return task_id
            else:
                print(f"❌ 训练启动失败: {result.get('error')}")
                return None
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 启动训练异常: {e}")
        return None

def monitor_training_with_detailed_analysis(session, task_id, duration=300):
    """详细监控训练进度"""
    print(f"\n📊 详细监控训练进度 (任务: {task_id[:8]}...)")
    print("=" * 60)
    
    start_time = time.time()
    last_progress = -1
    last_epoch = -1
    last_loss = -1
    progress_updates = 0
    epoch_changes = 0
    loss_changes = 0
    
    training_phases = {
        'data_preparation': False,
        'data_ready': False,
        'model_training': False,
        'training_started': False,
        'epoch_completed': False
    }
    
    for i in range(duration // 5):  # 每5秒检查一次
        try:
            elapsed = time.time() - start_time
            
            # API检查
            response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    progress_data = result['progress']
                    progress = progress_data.get('progress', 0)
                    epoch = progress_data.get('epoch', 0)
                    status = progress_data.get('status', 'unknown')
                    train_loss = progress_data.get('train_loss', 0)
                    val_loss = progress_data.get('val_loss', 0)
                    
                    # 解析日志获取阶段信息
                    logs = progress_data.get('logs')
                    stage = 'unknown'
                    message = ''
                    
                    if logs:
                        try:
                            log_data = json.loads(logs)
                            stage = log_data.get('stage', 'unknown')
                            message = log_data.get('message', '')
                        except:
                            pass
                    
                    print(f"[{elapsed:6.1f}s] 进度: {progress:5.1f}%, 轮次: {epoch:2d}, 状态: {status:12s}, 阶段: {stage:15s}, 损失: {train_loss:.4f}")
                    
                    # 跟踪训练阶段
                    if stage == 'data_preparation' and not training_phases['data_preparation']:
                        training_phases['data_preparation'] = True
                        print(f"        🔄 进入数据准备阶段")
                    
                    if stage == 'data_ready' and not training_phases['data_ready']:
                        training_phases['data_ready'] = True
                        print(f"        ✅ 数据准备完成")
                    
                    if stage == 'model_training' and not training_phases['model_training']:
                        training_phases['model_training'] = True
                        print(f"        🚀 进入模型训练阶段")
                    
                    # 检查训练是否真正开始
                    if not training_phases['training_started'] and (epoch > 0 or train_loss > 0):
                        training_phases['training_started'] = True
                        print(f"        🎉 训练循环成功启动！")
                    
                    # 检查epoch是否完成
                    if not training_phases['epoch_completed'] and epoch >= 1:
                        training_phases['epoch_completed'] = True
                        print(f"        🏆 第一个epoch完成！")
                    
                    # 检查进度更新
                    if progress != last_progress:
                        progress_updates += 1
                        print(f"        📈 进度更新 #{progress_updates}: {last_progress:.1f}% -> {progress:.1f}%")
                    
                    if epoch != last_epoch and epoch > last_epoch:
                        epoch_changes += 1
                        print(f"        🔢 轮次增加 #{epoch_changes}: {last_epoch} -> {epoch}")
                    
                    if train_loss != last_loss and train_loss > 0:
                        loss_changes += 1
                        print(f"        📉 损失变化 #{loss_changes}: {last_loss:.4f} -> {train_loss:.4f}")
                    
                    last_progress = progress
                    last_epoch = epoch
                    last_loss = train_loss
                    
                    # 检查训练是否完成
                    if status in ['completed', 'failed', 'stopped']:
                        print(f"        🏁 训练结束: {status}")
                        break
                    
                    # 如果训练正常进行，认为修复成功
                    if (training_phases['training_started'] and 
                        progress_updates >= 5 and 
                        epoch_changes >= 1):
                        print(f"        ✅ 训练进度正常，修复成功！")
                        break
                        
                else:
                    print(f"[{elapsed:6.1f}s] ❌ API错误: {result.get('error')}")
                    
            else:
                print(f"[{elapsed:6.1f}s] ❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"[{elapsed:6.1f}s] ❌ 监控异常: {e}")
        
        time.sleep(5)
    
    # 分析结果
    print(f"\n📊 监控结果分析:")
    print(f"   监控时长: {elapsed:.1f}秒")
    print(f"   进度更新次数: {progress_updates}")
    print(f"   轮次变化次数: {epoch_changes}")
    print(f"   损失变化次数: {loss_changes}")
    
    print(f"\n🎯 训练阶段完成情况:")
    for phase, completed in training_phases.items():
        status = "✅" if completed else "❌"
        print(f"   {status} {phase}")
    
    # 判断修复是否成功
    success_criteria = [
        training_phases['model_training'],  # 进入训练阶段
        training_phases['training_started'],  # 训练真正开始
        progress_updates >= 3,  # 有足够的进度更新
        epoch_changes >= 1 or loss_changes >= 1  # 有实际的训练进展
    ]
    
    success_count = sum(success_criteria)
    total_criteria = len(success_criteria)
    
    print(f"\n🎯 修复成功评估: {success_count}/{total_criteria}")
    
    if success_count >= 3:
        print("✅ 训练进度修复成功！")
        return True
    elif success_count >= 2:
        print("⚠️ 训练进度部分修复")
        return False
    else:
        print("❌ 训练进度修复失败")
        return False

def main():
    """主测试函数"""
    print("🧪 最终训练进度修复测试")
    print("=" * 60)
    
    # 1. 登录
    session = login_session()
    if not session:
        return
    
    # 2. 停止所有运行中的任务
    if not stop_all_running_tasks(session):
        print("⚠️ 停止现有任务失败，继续测试")
    
    # 3. 启动测试训练
    task_id = start_test_training(session)
    if not task_id:
        return
    
    # 4. 详细监控训练
    success = monitor_training_with_detailed_analysis(session, task_id)
    
    # 5. 总结结果
    print("\n" + "=" * 60)
    print("📋 最终测试结果:")
    
    if success:
        print("🎉 训练进度修复完全成功！")
        print("   ✅ 训练循环正常启动")
        print("   ✅ 进度正确更新")
        print("   ✅ 轮次正常递增")
        print("   ✅ 损失值正常计算")
        print("   ✅ 状态转换正常")
    else:
        print("⚠️ 训练进度修复需要进一步优化")
        print("   - 部分功能正常工作")
        print("   - 可能仍有卡住的情况")
    
    print("\n💡 建议:")
    if success:
        print("   - 可以正常使用训练功能")
        print("   - 建议使用适中的配置参数")
        print("   - 监控训练过程确保稳定性")
    else:
        print("   - 继续调试训练循环")
        print("   - 检查数据加载器性能")
        print("   - 考虑简化模型配置")

if __name__ == "__main__":
    main()
