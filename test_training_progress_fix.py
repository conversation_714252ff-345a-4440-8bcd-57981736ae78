#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试训练进度修复效果
"""

import requests
import json
import time
import sqlite3
from datetime import datetime

def login_session():
    """登录获取会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        
        if response.status_code == 200:
            print("✅ 登录成功")
            return session
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def stop_existing_training(session):
    """停止现有的训练任务"""
    print("\n🛑 停止现有训练任务")
    print("=" * 40)
    
    try:
        # 获取运行中的任务
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id FROM training_tasks
            WHERE status = 'running'
            ORDER BY updated_at DESC
            LIMIT 1
        ''')
        
        task = cursor.fetchone()
        conn.close()
        
        if task:
            task_id = task[0]
            print(f"📋 找到运行中的任务: {task_id[:8]}...")
            
            # 停止任务
            response = session.post(f'http://127.0.0.1:5000/api/deep-learning/stop-training/{task_id}')
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print("✅ 任务停止成功")
                    time.sleep(2)  # 等待状态更新
                    return True
                else:
                    print(f"❌ 停止失败: {result.get('error')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
        else:
            print("ℹ️ 没有找到运行中的任务")
            return True
            
    except Exception as e:
        print(f"❌ 停止任务失败: {e}")
        return False

def start_minimal_training(session):
    """启动最小化训练测试"""
    print("\n🚀 启动最小化训练测试")
    print("=" * 40)
    
    # 使用最小化配置
    config = {
        'model_name': f'PROGRESS_TEST_{int(time.time())}',
        'model_type': 'lstm',
        'symbol': 'XAUUSD',
        'timeframe': 'H1',
        'epochs': 5,  # 只训练5轮
        'batch_size': 4,  # 很小的批次
        'learning_rate': 0.01,
        'sequence_length': 10,  # 短序列
        'data_config': {
            'days': 5,  # 只用5天数据
            'validation_split': 0.2
        },
        'early_stopping': False,  # 禁用早停
        'save_checkpoints': False  # 禁用检查点
    }
    
    print(f"📝 测试配置:")
    for key, value in config.items():
        if key != 'data_config':
            print(f"   {key}: {value}")
    
    try:
        response = session.post(
            'http://127.0.0.1:5000/api/deep-learning/start-training',
            json=config,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"✅ 训练启动成功!")
                print(f"   任务ID: {task_id}")
                return task_id
            else:
                print(f"❌ 训练启动失败: {result.get('error')}")
                return None
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 启动训练异常: {e}")
        return None

def monitor_training_progress(session, task_id, duration=180):
    """详细监控训练进度"""
    print(f"\n📊 详细监控训练进度 (任务: {task_id[:8]}...)")
    print("=" * 60)
    
    last_progress = -1
    last_epoch = -1
    last_loss = -1
    progress_updates = 0
    stuck_count = 0
    training_started = False
    
    start_time = time.time()
    
    for i in range(duration // 3):  # 每3秒检查一次
        try:
            # API检查
            response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    progress_data = result['progress']
                    progress = progress_data.get('progress', 0)
                    epoch = progress_data.get('epoch', 0)
                    status = progress_data.get('status', 'unknown')
                    train_loss = progress_data.get('train_loss', 0)
                    val_loss = progress_data.get('val_loss', 0)
                    
                    # 解析日志获取更多信息
                    logs = progress_data.get('logs')
                    stage = 'unknown'
                    message = ''
                    
                    if logs:
                        try:
                            log_data = json.loads(logs)
                            stage = log_data.get('stage', 'unknown')
                            message = log_data.get('message', '')
                        except:
                            pass
                    
                    elapsed = time.time() - start_time
                    print(f"[{elapsed:6.1f}s] 进度: {progress:5.1f}%, 轮次: {epoch:2d}, 状态: {status:12s}, 阶段: {stage:15s}, 训练损失: {train_loss:.4f}")
                    
                    # 检查进度更新
                    if progress != last_progress or epoch != last_epoch or train_loss != last_loss:
                        progress_updates += 1
                        stuck_count = 0
                        print(f"        ✅ 进度更新 #{progress_updates}")
                        
                        # 检查训练是否真正开始
                        if not training_started and (epoch > 0 or train_loss > 0):
                            training_started = True
                            print(f"        🎉 训练循环成功启动！")
                        
                        # 检查是否有实际的训练进展
                        if epoch > last_epoch:
                            print(f"        📈 轮次增加: {last_epoch} -> {epoch}")
                        
                        if train_loss > 0 and train_loss != last_loss:
                            print(f"        📉 损失变化: {last_loss:.4f} -> {train_loss:.4f}")
                        
                    else:
                        stuck_count += 1
                        if stuck_count >= 10:  # 30秒无变化
                            print(f"        ⚠️ 进度卡住 (连续{stuck_count}次无变化)")
                    
                    last_progress = progress
                    last_epoch = epoch
                    last_loss = train_loss
                    
                    # 检查训练是否完成
                    if status in ['completed', 'failed', 'stopped']:
                        print(f"        🏁 训练结束: {status}")
                        return status == 'completed'
                    
                    # 如果训练正常进行，认为修复成功
                    if training_started and progress_updates >= 3:
                        print(f"        ✅ 训练进度正常更新，修复成功！")
                        return True
                        
                else:
                    print(f"[{elapsed:6.1f}s] ❌ API错误: {result.get('error')}")
                    
            else:
                print(f"[{elapsed:6.1f}s] ❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"[{elapsed:6.1f}s] ❌ 监控异常: {e}")
        
        time.sleep(3)
    
    print("⏰ 监控超时")
    return False

def check_database_updates(task_id):
    """直接检查数据库更新"""
    print(f"\n🔍 直接检查数据库更新")
    print("-" * 40)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT progress, current_epoch, train_loss, val_loss, updated_at
            FROM training_tasks
            WHERE id = ?
        ''', (task_id,))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            progress, epoch, train_loss, val_loss, updated_at = result
            print(f"📊 数据库状态:")
            print(f"   进度: {progress}%")
            print(f"   轮次: {epoch}")
            print(f"   训练损失: {train_loss}")
            print(f"   验证损失: {val_loss}")
            print(f"   更新时间: {updated_at}")
            
            # 检查更新时间
            try:
                updated_time = datetime.fromisoformat(updated_at.replace('T', ' '))
                now = datetime.now()
                time_diff = now - updated_time
                print(f"   距离现在: {time_diff}")
                
                if time_diff.total_seconds() < 60:
                    print("   ✅ 最近有更新")
                else:
                    print("   ⚠️ 更新时间较久")
            except:
                print("   ❌ 时间解析失败")
        else:
            print("❌ 任务不存在")
            
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")

def main():
    """主测试函数"""
    print("🧪 训练进度修复效果测试")
    print("=" * 60)
    
    # 1. 登录
    session = login_session()
    if not session:
        return
    
    # 2. 停止现有训练
    if not stop_existing_training(session):
        print("⚠️ 停止现有训练失败，继续测试")
    
    # 3. 启动最小化训练
    task_id = start_minimal_training(session)
    if not task_id:
        return
    
    # 4. 详细监控训练
    success = monitor_training_progress(session, task_id)
    
    # 5. 检查数据库状态
    check_database_updates(task_id)
    
    # 6. 总结结果
    print("\n" + "=" * 60)
    print("📋 测试结果:")
    
    if success:
        print("✅ 训练进度修复成功！")
        print("   - 训练循环正常启动")
        print("   - 进度正确更新")
        print("   - 轮次正常递增")
        print("   - 损失值正常计算")
    else:
        print("❌ 训练进度修复失败")
        print("   - 训练可能仍然卡住")
        print("   - 需要进一步调试")
    
    print("\n💡 建议:")
    if success:
        print("   - 可以正常使用训练功能")
        print("   - 建议使用适中的配置参数")
    else:
        print("   - 检查系统资源使用情况")
        print("   - 查看应用程序日志")
        print("   - 考虑重启训练服务")

if __name__ == "__main__":
    main()
