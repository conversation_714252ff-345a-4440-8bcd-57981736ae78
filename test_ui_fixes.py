#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI修复效果
验证弹窗和状态检查的修复是否有效
"""

import requests
import json
import time
from datetime import datetime

def login_session():
    """登录获取会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        
        if response.status_code == 200:
            print("✅ 登录成功")
            return session
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def check_current_training_status(session):
    """检查当前训练状态"""
    print("\n🔍 检查当前训练任务状态")
    print("=" * 40)
    
    try:
        # 获取所有训练任务
        import sqlite3
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, status, progress, logs, updated_at
            FROM training_tasks
            WHERE status IN ('running', 'data_ready')
            ORDER BY updated_at DESC
            LIMIT 5
        ''')
        
        tasks = cursor.fetchall()
        conn.close()
        
        if not tasks:
            print("ℹ️ 没有找到活跃的训练任务")
            return None
        
        print(f"📊 找到 {len(tasks)} 个活跃任务:")
        
        for task in tasks:
            task_id, status, progress, logs, updated_at = task
            
            # 解析日志获取阶段信息
            stage = 'unknown'
            if logs:
                try:
                    log_data = json.loads(logs)
                    stage = log_data.get('stage', 'unknown')
                except:
                    pass
            
            print(f"   📋 {task_id[:8]}... | {status} | {stage} | {progress}% | {updated_at}")
            
            # 测试API状态检查
            response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    api_progress = result['progress']
                    print(f"     API状态: {api_progress['status']} | 进度: {api_progress['progress']}%")
        
        return tasks[0][0] if tasks else None  # 返回最新的任务ID
        
    except Exception as e:
        print(f"❌ 检查状态失败: {e}")
        return None

def test_training_start_with_running_task(session, task_id):
    """测试在有运行中任务时启动训练"""
    print(f"\n🧪 测试训练启动逻辑 (任务: {task_id[:8]}...)")
    print("=" * 50)
    
    try:
        # 首先检查任务状态
        response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                progress = result['progress']
                status = progress['status']
                
                print(f"📊 当前任务状态: {status}")
                
                # 解析日志获取阶段
                stage = 'unknown'
                if progress.get('logs'):
                    try:
                        log_data = json.loads(progress['logs'])
                        stage = log_data.get('stage', 'unknown')
                    except:
                        pass
                
                print(f"📋 当前阶段: {stage}")
                
                # 模拟前端状态检查逻辑
                can_train = False
                message = ""
                
                if status == 'data_ready':
                    can_train = True
                    message = "数据准备完成，可以开始训练"
                elif status == 'running':
                    if stage == 'model_training':
                        can_train = False
                        message = "模型训练已在进行中。如需重新训练，请先停止当前训练或等待训练完成"
                    elif stage == 'data_preparation':
                        can_train = False
                        message = "数据准备进行中，请等待数据准备完成"
                    elif stage == 'data_ready':
                        can_train = False
                        message = "数据准备已完成，但状态同步中，请稍等片刻后重试"
                
                print(f"🎯 前端判断结果:")
                print(f"   可以训练: {can_train}")
                print(f"   提示消息: {message}")
                
                # 如果是模型训练阶段，测试停止功能
                if status == 'running' and stage == 'model_training':
                    print(f"\n🛑 测试停止训练功能...")
                    
                    user_choice = input("是否测试停止当前训练？(y/n): ").lower().strip()
                    
                    if user_choice in ['y', 'yes', '是']:
                        stop_response = session.post(f'http://127.0.0.1:5000/api/deep-learning/stop-training/{task_id}')
                        
                        if stop_response.status_code == 200:
                            stop_result = stop_response.json()
                            
                            if stop_result.get('success'):
                                print("✅ 训练停止成功")
                                
                                # 等待状态更新
                                print("⏳ 等待状态更新...")
                                time.sleep(3)
                                
                                # 重新检查状态
                                new_response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
                                if new_response.status_code == 200:
                                    new_result = new_response.json()
                                    if new_result.get('success'):
                                        new_status = new_result['progress']['status']
                                        print(f"📊 更新后状态: {new_status}")
                            else:
                                print(f"❌ 停止训练失败: {stop_result.get('error')}")
                        else:
                            print(f"❌ 停止训练请求失败: {stop_response.status_code}")
                
                return True
                
            else:
                print(f"❌ 获取状态失败: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_data_preparation_dialog_fix():
    """测试数据准备完成对话框修复"""
    print(f"\n🧪 测试数据准备完成对话框修复")
    print("=" * 50)
    
    print("📋 修复内容:")
    print("1. ✅ 添加了 dataReadyDialogShown 标志防止重复显示")
    print("2. ✅ 在数据准备开始时重置标志")
    print("3. ✅ 只在第一次检测到 data_ready 状态时显示对话框")
    
    print("\n💡 使用说明:")
    print("- 现在数据准备完成对话框只会显示一次")
    print("- 不需要多次点击确定按钮")
    print("- 重新开始数据准备时会重置对话框状态")

def main():
    """主测试函数"""
    print("🧪 UI修复效果测试")
    print("=" * 60)
    
    # 1. 登录
    session = login_session()
    if not session:
        return
    
    # 2. 检查当前训练状态
    current_task_id = check_current_training_status(session)
    
    # 3. 测试训练启动逻辑
    if current_task_id:
        test_training_start_with_running_task(session, current_task_id)
    else:
        print("\n💡 建议:")
        print("1. 启动一个数据准备任务来测试弹窗修复")
        print("2. 启动一个模型训练任务来测试状态检查修复")
    
    # 4. 测试对话框修复
    test_data_preparation_dialog_fix()
    
    print(f"\n🎉 测试完成！")
    print("=" * 60)
    print("📋 修复总结:")
    print("✅ 问题1: 数据准备完成弹窗重复显示 - 已修复")
    print("   - 添加了防重复显示机制")
    print("   - 弹窗只会显示一次")
    
    print("✅ 问题2: 训练启动时状态检查过严 - 已修复")
    print("   - 改进了状态检查逻辑")
    print("   - 提供了停止当前训练的选项")
    print("   - 增加了更详细的状态说明")
    
    print("\n💡 用户体验改进:")
    print("- 更清晰的状态提示信息")
    print("- 更智能的交互逻辑")
    print("- 更友好的错误处理")

if __name__ == "__main__":
    main()
